// Testing Infrastructure Improvements

package testing

import (
	"context"
	"database/sql"
	"fmt"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// 1. TEST SUITE BASE
type BaseTestSuite struct {
	suite.Suite
	DB     *gorm.DB
	Router *gin.Engine
	TestDB *sql.DB
}

func (s *BaseTestSuite) SetupSuite() {
	// Setup test database
	testDB := s.setupTestDatabase()
	s.TestDB = testDB

	// Setup GORM
	gormDB, err := gorm.Open(postgres.New(postgres.Config{
		Conn: testDB,
	}), &gorm.Config{})
	s.Require().NoError(err)
	s.DB = gormDB

	// Setup Gin router
	gin.SetMode(gin.TestMode)
	s.Router = gin.New()
}

func (s *BaseTestSuite) TearDownSuite() {
	if s.TestDB != nil {
		s.TestDB.Close()
	}
}

func (s *BaseTestSuite) SetupTest() {
	// Clean database before each test
	s.cleanDatabase()
}

func (s *BaseTestSuite) TearDownTest() {
	// Clean database after each test
	s.cleanDatabase()
}

func (s *BaseTestSuite) setupTestDatabase() *sql.DB {
	// Connect to test database
	dsn := "host=localhost user=gokeys password=gokeys_dev_password dbname=gokeys_test port=5432 sslmode=disable"
	db, err := sql.Open("postgres", dsn)
	s.Require().NoError(err)

	// Run migrations
	s.runMigrations(db)

	return db
}

func (s *BaseTestSuite) runMigrations(db *sql.DB) {
	// Run database migrations for tests
	// This should use the same migration system as the main app
}

func (s *BaseTestSuite) cleanDatabase() {
	// Clean all tables in reverse dependency order
	tables := []string{
		"machines", "licenses", "policies", "products", 
		"users_organizations", "users", "organizations",
	}

	for _, table := range tables {
		s.DB.Exec(fmt.Sprintf("DELETE FROM %s", table))
	}
}

// 2. TEST DATA FACTORY
type TestDataFactory struct {
	db *gorm.DB
}

func NewTestDataFactory(db *gorm.DB) *TestDataFactory {
	return &TestDataFactory{db: db}
}

func (f *TestDataFactory) CreateOrganization(overrides ...func(*entities.Organization)) *entities.Organization {
	org := &entities.Organization{
		ID:        uuid.New(),
		Name:      "Test Organization " + uuid.New().String()[:8],
		Slug:      "test-org-" + uuid.New().String()[:8],
		Email:     fmt.Sprintf("<EMAIL>", uuid.New().String()[:8]),
		Type:      entities.OrganizationTypeVendor,
		Status:    entities.OrganizationStatusActive,
		Protected: false,
		Settings:  make(entities.OrganizationSettings),
		Metadata:  make(entities.Metadata),
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Apply overrides
	for _, override := range overrides {
		override(org)
	}

	// Save to database
	err := f.db.Create(org).Error
	if err != nil {
		panic(fmt.Sprintf("Failed to create test organization: %v", err))
	}

	return org
}

func (f *TestDataFactory) CreateProduct(org *entities.Organization, overrides ...func(*entities.Product)) *entities.Product {
	product := &entities.Product{
		ID:             uuid.New(),
		OrganizationID: org.ID,
		Name:           "Test Product " + uuid.New().String()[:8],
		Code:           "test-product-" + uuid.New().String()[:8],
		Description:    "Test product description",
		URL:            "https://example.com/test-product",
		Platforms:      entities.ProductPlatforms{"linux", "windows"},
		Metadata:       make(entities.Metadata),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Apply overrides
	for _, override := range overrides {
		override(product)
	}

	// Save to database
	err := f.db.Create(product).Error
	if err != nil {
		panic(fmt.Sprintf("Failed to create test product: %v", err))
	}

	return product
}

func (f *TestDataFactory) CreatePolicy(product *entities.Product, overrides ...func(*entities.Policy)) *entities.Policy {
	policy := &entities.Policy{
		ID:             uuid.New(),
		ProductID:      product.ID,
		OrganizationID: product.OrganizationID,
		Name:           "Test Policy " + uuid.New().String()[:8],
		Duration:       nil, // No expiration
		Encrypted:      false,
		UsePool:        false,
		Strict:         true,
		Floating:       false,
		Protected:      false,
		MaxMachines:    &[]int{1}[0],
		Metadata:       make(entities.Metadata),
		CreatedAt:      time.Now(),
		UpdatedAt:      time.Now(),
	}

	// Apply overrides
	for _, override := range overrides {
		override(policy)
	}

	// Save to database
	err := f.db.Create(policy).Error
	if err != nil {
		panic(fmt.Sprintf("Failed to create test policy: %v", err))
	}

	return policy
}

// 3. MOCK REPOSITORIES
type MockOrganizationRepository struct {
	mock.Mock
}

func (m *MockOrganizationRepository) Create(ctx context.Context, org *entities.Organization) error {
	args := m.Called(ctx, org)
	return args.Error(0)
}

func (m *MockOrganizationRepository) GetByID(ctx context.Context, id uuid.UUID) (*entities.Organization, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*entities.Organization), args.Error(1)
}

func (m *MockOrganizationRepository) GetBySlug(ctx context.Context, slug string) (*entities.Organization, error) {
	args := m.Called(ctx, slug)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*entities.Organization), args.Error(1)
}

func (m *MockOrganizationRepository) Update(ctx context.Context, org *entities.Organization) error {
	args := m.Called(ctx, org)
	return args.Error(0)
}

func (m *MockOrganizationRepository) Delete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockOrganizationRepository) SoftDelete(ctx context.Context, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockOrganizationRepository) List(ctx context.Context, filter repositories.ListFilter) ([]*entities.Organization, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.Organization), args.Get(1).(int64), args.Error(2)
}

func (m *MockOrganizationRepository) Count(ctx context.Context, filter repositories.ListFilter) (int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).(int64), args.Error(1)
}

func (m *MockOrganizationRepository) Exists(ctx context.Context, id uuid.UUID) (bool, error) {
	args := m.Called(ctx, id)
	return args.Bool(0), args.Error(1)
}

// 4. HTTP TEST UTILITIES
type HTTPTestUtils struct {
	router *gin.Engine
}

func NewHTTPTestUtils(router *gin.Engine) *HTTPTestUtils {
	return &HTTPTestUtils{router: router}
}

func (h *HTTPTestUtils) MakeRequest(method, path string, body interface{}) *httptest.ResponseRecorder {
	var bodyReader io.Reader
	if body != nil {
		jsonBody, _ := json.Marshal(body)
		bodyReader = bytes.NewReader(jsonBody)
	}

	req := httptest.NewRequest(method, path, bodyReader)
	if body != nil {
		req.Header.Set("Content-Type", "application/json")
	}

	w := httptest.NewRecorder()
	h.router.ServeHTTP(w, req)
	return w
}

func (h *HTTPTestUtils) AssertJSONResponse(t *testing.T, w *httptest.ResponseRecorder, expectedStatus int, expectedBody interface{}) {
	assert.Equal(t, expectedStatus, w.Code)
	assert.Equal(t, "application/json; charset=utf-8", w.Header().Get("Content-Type"))

	if expectedBody != nil {
		var actualBody interface{}
		err := json.Unmarshal(w.Body.Bytes(), &actualBody)
		assert.NoError(t, err)

		expectedJSON, _ := json.Marshal(expectedBody)
		var expectedBodyParsed interface{}
		json.Unmarshal(expectedJSON, &expectedBodyParsed)

		assert.Equal(t, expectedBodyParsed, actualBody)
	}
}

// 5. INTEGRATION TEST EXAMPLE
type OrganizationIntegrationTestSuite struct {
	BaseTestSuite
	factory *TestDataFactory
	handler *handlers.OrganizationHandler
}

func (s *OrganizationIntegrationTestSuite) SetupTest() {
	s.BaseTestSuite.SetupTest()
	s.factory = NewTestDataFactory(s.DB)
	
	// Setup handler with real repository
	orgRepo := repositories.NewOrganizationRepository(s.DB)
	s.handler = handlers.NewOrganizationHandler(orgRepo)
	
	// Setup routes
	s.Router.GET("/organizations/:id", s.handler.GetOrganization)
	s.Router.POST("/organizations", s.handler.CreateOrganization)
}

func (s *OrganizationIntegrationTestSuite) TestCreateOrganization() {
	// Test data
	createReq := handlers.CreateOrganizationRequest{
		Name:  "Test Organization",
		Slug:  "test-org",
		Email: "<EMAIL>",
		Type:  entities.OrganizationTypeVendor,
	}

	// Make request
	httpUtils := NewHTTPTestUtils(s.Router)
	w := httpUtils.MakeRequest("POST", "/organizations", createReq)

	// Assert response
	s.Equal(http.StatusCreated, w.Code)
	
	var response handlers.OrganizationResponse
	err := json.Unmarshal(w.Body.Bytes(), &response)
	s.NoError(err)
	s.Equal(createReq.Name, response.Name)
	s.Equal(createReq.Slug, response.Slug)
	s.Equal(createReq.Email, response.Email)
}

func TestOrganizationIntegrationSuite(t *testing.T) {
	suite.Run(t, new(OrganizationIntegrationTestSuite))
}

// 6. UNIT TEST EXAMPLE
func TestOrganizationService_CreateOrganization(t *testing.T) {
	// Setup mocks
	mockRepo := new(MockOrganizationRepository)
	logger := zerolog.New(os.Stdout)
	service := services.NewOrganizationService(mockRepo, logger)

	// Test data
	req := services.CreateOrganizationRequest{
		Name:  "Test Organization",
		Slug:  "test-org",
		Email: "<EMAIL>",
		Type:  entities.OrganizationTypeVendor,
	}

	// Setup expectations
	mockRepo.On("GetBySlug", mock.Anything, req.Slug).Return(nil, gorm.ErrRecordNotFound)
	mockRepo.On("Create", mock.Anything, mock.AnythingOfType("*entities.Organization")).Return(nil)

	// Execute
	org, err := service.CreateOrganization(context.Background(), req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, org)
	assert.Equal(t, req.Name, org.Name)
	assert.Equal(t, req.Slug, org.Slug)
	assert.Equal(t, req.Email, org.Email)

	// Verify mock expectations
	mockRepo.AssertExpectations(t)
}

// 7. BENCHMARK TESTS
func BenchmarkOrganizationRepository_Create(b *testing.B) {
	// Setup test database
	db := setupBenchmarkDB(b)
	repo := repositories.NewOrganizationRepository(db)
	factory := NewTestDataFactory(db)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		org := factory.CreateOrganization()
		_ = repo.Create(context.Background(), org)
	}
}
