// Common Patterns and Code Deduplication

package common

import (
	"context"
	"fmt"
	"net/http"
	"regexp"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// 1. CENTRALIZED VALIDATION UTILITIES
type Validator struct{}

var (
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	slugRegex  = regexp.MustCompile(`^[a-z0-9-]+$`)
)

func (v *Validator) ValidateUUID(id string, fieldName string) error {
	if id == "" {
		return fmt.Errorf("%s is required", fieldName)
	}
	if _, err := uuid.Parse(id); err != nil {
		return fmt.Errorf("invalid %s format", fieldName)
	}
	return nil
}

func (v *Validator) ValidateEmail(email string) error {
	if email == "" {
		return fmt.Errorf("email is required")
	}
	if !emailRegex.MatchString(email) {
		return fmt.Errorf("invalid email format")
	}
	return nil
}

func (v *Validator) ValidateSlug(slug string) error {
	if slug == "" {
		return fmt.Errorf("slug is required")
	}
	if len(slug) < 3 || len(slug) > 50 {
		return fmt.Errorf("slug must be between 3 and 50 characters")
	}
	if !slugRegex.MatchString(slug) {
		return fmt.Errorf("slug can only contain lowercase letters, numbers, and hyphens")
	}
	return nil
}

func (v *Validator) ValidateRequired(value string, fieldName string) error {
	if strings.TrimSpace(value) == "" {
		return fmt.Errorf("%s is required", fieldName)
	}
	return nil
}

func (v *Validator) ValidateLength(value string, fieldName string, min, max int) error {
	length := len(strings.TrimSpace(value))
	if length < min {
		return fmt.Errorf("%s must be at least %d characters", fieldName, min)
	}
	if length > max {
		return fmt.Errorf("%s must be at most %d characters", fieldName, max)
	}
	return nil
}

func (v *Validator) ValidateEnum(value string, fieldName string, validValues []string) error {
	for _, valid := range validValues {
		if value == valid {
			return nil
		}
	}
	return fmt.Errorf("%s must be one of: %s", fieldName, strings.Join(validValues, ", "))
}

// 2. GENERIC REPOSITORY PATTERN
type GenericRepository[T any] struct {
	db        *gorm.DB
	tableName string
}

func NewGenericRepository[T any](db *gorm.DB, tableName string) *GenericRepository[T] {
	return &GenericRepository[T]{
		db:        db,
		tableName: tableName,
	}
}

func (r *GenericRepository[T]) Create(ctx context.Context, entity *T) error {
	return r.db.WithContext(ctx).Create(entity).Error
}

func (r *GenericRepository[T]) GetByID(ctx context.Context, id uuid.UUID) (*T, error) {
	var entity T
	err := r.db.WithContext(ctx).Where("id = ? AND deleted_at IS NULL", id).First(&entity).Error
	if err != nil {
		return nil, err
	}
	return &entity, nil
}

func (r *GenericRepository[T]) Update(ctx context.Context, entity *T) error {
	return r.db.WithContext(ctx).Save(entity).Error
}

func (r *GenericRepository[T]) SoftDelete(ctx context.Context, id uuid.UUID) error {
	return r.db.WithContext(ctx).Model(new(T)).Where("id = ?", id).Update("deleted_at", time.Now()).Error
}

func (r *GenericRepository[T]) List(ctx context.Context, filter ListFilter) ([]*T, int64, error) {
	var entities []*T
	var total int64

	query := r.db.WithContext(ctx).Model(new(T)).Where("deleted_at IS NULL")

	// Apply filters
	if filter.Search != "" {
		query = query.Where("name ILIKE ?", "%"+filter.Search+"%")
	}

	// Count total
	if err := query.Count(&total).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	offset := (filter.Page - 1) * filter.PageSize
	if err := query.Offset(offset).Limit(filter.PageSize).Find(&entities).Error; err != nil {
		return nil, 0, err
	}

	return entities, total, nil
}

// 3. GENERIC CRUD SERVICE PATTERN
type CRUDService[T any, CreateReq any, UpdateReq any] struct {
	repository Repository[T]
	validator  *Validator
	logger     zerolog.Logger
}

func NewCRUDService[T any, CreateReq any, UpdateReq any](
	repo Repository[T],
	logger zerolog.Logger,
) *CRUDService[T, CreateReq, UpdateReq] {
	return &CRUDService[T, CreateReq, UpdateReq]{
		repository: repo,
		validator:  &Validator{},
		logger:     logger,
	}
}

func (s *CRUDService[T, CreateReq, UpdateReq]) Create(
	ctx context.Context,
	req CreateReq,
	transform func(CreateReq) (*T, error),
) (*T, error) {
	// Transform request to entity
	entity, err := transform(req)
	if err != nil {
		return nil, fmt.Errorf("transformation failed: %w", err)
	}

	// Save to repository
	if err := s.repository.Create(ctx, entity); err != nil {
		s.logger.Error().Err(err).Msg("Failed to create entity")
		return nil, fmt.Errorf("failed to create entity: %w", err)
	}

	return entity, nil
}

func (s *CRUDService[T, CreateReq, UpdateReq]) GetByID(ctx context.Context, id uuid.UUID) (*T, error) {
	entity, err := s.repository.GetByID(ctx, id)
	if err != nil {
		s.logger.Error().Err(err).Str("id", id.String()).Msg("Failed to get entity")
		return nil, fmt.Errorf("failed to get entity: %w", err)
	}
	return entity, nil
}

// 4. HTTP UTILITIES
type HTTPUtils struct{}

func (h *HTTPUtils) ParseUUIDParam(c *gin.Context, paramName string) (uuid.UUID, error) {
	idStr := c.Param(paramName)
	id, err := uuid.Parse(idStr)
	if err != nil {
		return uuid.Nil, fmt.Errorf("invalid %s format", paramName)
	}
	return id, nil
}

func (h *HTTPUtils) ParsePagination(c *gin.Context) (page int, limit int) {
	page, _ = strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ = strconv.Atoi(c.DefaultQuery("limit", "20"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 20
	}

	return page, limit
}

func (h *HTTPUtils) BindAndValidate(c *gin.Context, req interface{}) error {
	if err := c.ShouldBindJSON(req); err != nil {
		return fmt.Errorf("invalid request format: %w", err)
	}

	if validator, ok := req.(interface{ Validate() error }); ok {
		if err := validator.Validate(); err != nil {
			return fmt.Errorf("validation failed: %w", err)
		}
	}

	return nil
}

// 5. RESPONSE UTILITIES
type ResponseUtils struct{}

func (r *ResponseUtils) Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, gin.H{"data": data})
}

func (r *ResponseUtils) Created(c *gin.Context, data interface{}) {
	c.JSON(http.StatusCreated, gin.H{"data": data})
}

func (r *ResponseUtils) NoContent(c *gin.Context) {
	c.JSON(http.StatusNoContent, nil)
}

func (r *ResponseUtils) Error(c *gin.Context, statusCode int, code string, message string) {
	c.JSON(statusCode, gin.H{
		"error": gin.H{
			"code":    code,
			"message": message,
		},
	})
}

func (r *ResponseUtils) ValidationError(c *gin.Context, err error) {
	r.Error(c, http.StatusBadRequest, "VALIDATION_ERROR", err.Error())
}

func (r *ResponseUtils) NotFound(c *gin.Context, entityName string) {
	r.Error(c, http.StatusNotFound, entityName+"_NOT_FOUND", entityName+" not found")
}

func (r *ResponseUtils) InternalError(c *gin.Context, message string) {
	r.Error(c, http.StatusInternalServerError, "INTERNAL_ERROR", message)
}

// 6. MIDDLEWARE UTILITIES
func ValidationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add request ID for tracing
		requestID := uuid.New().String()
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)
		c.Next()
	}
}

func ErrorHandlingMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			log.Error().Str("error", err).Msg("Panic recovered")
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": gin.H{
					"code":    "INTERNAL_ERROR",
					"message": "Internal server error",
				},
			})
		}
		c.Abort()
	})
}

// 7. COMMON FILTER TYPES
type ListFilter struct {
	Page     int    `form:"page" binding:"min=1"`
	PageSize int    `form:"limit" binding:"min=1,max=100"`
	Search   string `form:"search"`
	SortBy   string `form:"sort_by"`
	SortDir  string `form:"sort_dir" binding:"oneof=asc desc"`
}

func (f *ListFilter) Validate() error {
	if f.Page < 1 {
		f.Page = 1
	}
	if f.PageSize < 1 || f.PageSize > 100 {
		f.PageSize = 20
	}
	if f.SortDir != "asc" && f.SortDir != "desc" {
		f.SortDir = "asc"
	}
	return nil
}

// 8. COMMON RESPONSE TYPES
type ListResponse[T any] struct {
	Data  []T   `json:"data"`
	Total int64 `json:"total"`
	Page  int   `json:"page"`
	Limit int   `json:"limit"`
	Pages int64 `json:"pages"`
}

func NewListResponse[T any](data []T, total int64, page, limit int) *ListResponse[T] {
	pages := (total + int64(limit) - 1) / int64(limit)
	return &ListResponse[T]{
		Data:  data,
		Total: total,
		Page:  page,
		Limit: limit,
		Pages: pages,
	}
}
