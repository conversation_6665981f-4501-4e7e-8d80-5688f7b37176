-- Database Migration and Schema Consistency Recommendations

-- 1. STAN<PERSON>RDIZED MIGRATION NAMING CONVENTION
-- Format: {version}_{action}_{table_name}_{description}.{up|down}.sql
-- Examples:
-- 001_create_core_tables.up.sql
-- 002_add_indexes_core_tables.up.sql
-- 003_alter_users_add_2fa_fields.up.sql

-- 2. CONSISTENT TABLE STRUCTURE PATTERN
-- All tables should follow this pattern:

-- Standard UUID primary key
-- id UUID PRIMARY KEY DEFAULT gen_random_uuid(),

-- Core business fields
-- name VARCHAR(255) NOT NULL,
-- description TEXT,

-- Status and flags
-- status VARCHAR(50) NOT NULL DEFAULT 'active',
-- protected BOOLEAN NOT NULL DEFAULT false,

-- Foreign keys (with consistent naming)
-- organization_id UUID NOT NULL REFERENCES organizations(id),
-- created_by_user_id UUID REFERENCES users(id),

-- J<PERSON>NB fields (with defaults)
-- settings JSONB NOT NULL DEFAULT '{}',
-- metadata JSONB NOT NULL DEFAULT '{}',

-- Timestamps (consistent across all tables)
-- created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
-- updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
-- deleted_at TIMESTAMP WITH TIME ZONE,

-- 3. MISSING COLUMNS AUDIT SCRIPT
-- Run this to find missing columns between entities and database

-- Check for missing columns in organizations table
SELECT 
    'organizations' as table_name,
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_name = 'organizations' 
    AND table_schema = 'public'
ORDER BY ordinal_position;

-- Expected columns based on Organization entity:
-- id, name, slug, email, type, status, protected
-- public_key, private_key, secret_key, ed25519_private_key, ed25519_public_key
-- max_users, max_licenses, max_machines
-- settings, metadata, created_at, updated_at, deleted_at

-- 4. COLUMN MAPPING VALIDATION QUERY
-- This query helps identify GORM column mapping issues

WITH entity_fields AS (
    SELECT 'organizations' as table_name, 'public_key' as go_field, 'public_key' as db_column
    UNION ALL SELECT 'organizations', 'private_key', 'private_key'
    UNION ALL SELECT 'organizations', 'secret_key', 'secret_key'
    UNION ALL SELECT 'organizations', 'ed25519_private_key', 'ed25519_private_key'
    UNION ALL SELECT 'organizations', 'ed25519_public_key', 'ed25519_public_key'
    UNION ALL SELECT 'organizations', 'max_users', 'max_users'
    UNION ALL SELECT 'organizations', 'max_licenses', 'max_licenses'
    UNION ALL SELECT 'organizations', 'max_machines', 'max_machines'
    -- Add more mappings for other entities
),
db_columns AS (
    SELECT 
        table_name,
        column_name
    FROM information_schema.columns 
    WHERE table_schema = 'public'
)
SELECT 
    ef.table_name,
    ef.go_field,
    ef.db_column,
    CASE 
        WHEN dc.column_name IS NULL THEN 'MISSING IN DB'
        ELSE 'OK'
    END as status
FROM entity_fields ef
LEFT JOIN db_columns dc ON ef.table_name = dc.table_name AND ef.db_column = dc.column_name
ORDER BY ef.table_name, ef.go_field;

-- 5. STANDARDIZED INDEX PATTERNS
-- Create consistent indexes across all tables

-- Primary key indexes (automatic)
-- Foreign key indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_organization_id ON products(organization_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_policies_product_id ON policies(product_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_licenses_policy_id ON licenses(policy_id);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_machines_license_id ON machines(license_id);

-- Unique constraint indexes
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_organizations_slug ON organizations(slug) WHERE deleted_at IS NULL;
CREATE UNIQUE INDEX CONCURRENTLY IF NOT EXISTS idx_users_email ON users(email) WHERE deleted_at IS NULL;

-- Query optimization indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_licenses_status ON licenses(status) WHERE deleted_at IS NULL;
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_machines_status ON machines(status) WHERE deleted_at IS NULL;

-- Soft delete indexes
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_organizations_deleted_at ON organizations(deleted_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_users_deleted_at ON users(deleted_at);
CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_deleted_at ON products(deleted_at);

-- 6. MIGRATION TEMPLATE
-- Use this template for new migrations

/*
-- Migration: {Description}
-- Version: {Version}
-- Date: {Date}
-- Author: {Author}

-- Up Migration
BEGIN;

-- Add your changes here
-- Example:
-- ALTER TABLE users ADD COLUMN new_field VARCHAR(255);
-- CREATE INDEX idx_users_new_field ON users(new_field);

-- Update migration version
-- INSERT INTO schema_migrations (version) VALUES ('{version}');

COMMIT;
*/

-- 7. ROLLBACK TEMPLATE
-- Always provide rollback scripts

/*
-- Rollback Migration: {Description}
-- Version: {Version}

BEGIN;

-- Rollback your changes here
-- Example:
-- DROP INDEX IF EXISTS idx_users_new_field;
-- ALTER TABLE users DROP COLUMN IF EXISTS new_field;

-- Remove migration version
-- DELETE FROM schema_migrations WHERE version = '{version}';

COMMIT;
*/

-- 8. DATA INTEGRITY CHECKS
-- Add these checks after migrations

-- Check for orphaned records
SELECT 'products' as table_name, COUNT(*) as orphaned_count
FROM products p
LEFT JOIN organizations o ON p.organization_id = o.id
WHERE o.id IS NULL;

SELECT 'licenses' as table_name, COUNT(*) as orphaned_count  
FROM licenses l
LEFT JOIN policies p ON l.policy_id = p.id
WHERE p.id IS NULL;

-- Check for invalid enum values
SELECT 'organizations' as table_name, type, COUNT(*) 
FROM organizations 
WHERE type NOT IN ('vendor', 'reseller', 'customer')
GROUP BY type;

-- Check for missing required fields
SELECT 'organizations' as table_name, COUNT(*) as missing_name_count
FROM organizations 
WHERE name IS NULL OR name = '';

-- 9. PERFORMANCE OPTIMIZATION QUERIES
-- Use these to identify slow queries and missing indexes

-- Find tables without primary keys
SELECT schemaname, tablename 
FROM pg_tables 
WHERE schemaname = 'public'
  AND tablename NOT IN (
    SELECT tablename 
    FROM pg_indexes 
    WHERE schemaname = 'public' 
      AND indexname LIKE '%_pkey'
  );

-- Find foreign keys without indexes
SELECT 
    tc.table_name,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND NOT EXISTS (
        SELECT 1 FROM pg_indexes 
        WHERE schemaname = 'public' 
            AND tablename = tc.table_name 
            AND indexdef LIKE '%' || kcu.column_name || '%'
    );
