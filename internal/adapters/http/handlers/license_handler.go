package handlers

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/crypto"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// LicenseHandler handles HTTP requests for license operations
type LicenseHandler struct {
	licenseRepo      repositories.LicenseRepository
	policyRepo       repositories.PolicyRepository
	organizationRepo repositories.OrganizationRepository
	userRepo         repositories.UserRepository
	cryptoService    *crypto.CryptoService
}

// NewLicenseHandler creates a new license handler
func NewLicenseHandler(
	licenseRepo repositories.LicenseRepository,
	policyRepo repositories.PolicyRepository,
	organizationRepo repositories.OrganizationRepository,
	userRepo repositories.UserRepository,
	cryptoService *crypto.CryptoService,
) *LicenseHandler {
	return &LicenseHandler{
		licenseRepo:      licenseRepo,
		policyRepo:       policyRepo,
		organizationRepo: organizationRepo,
		userRepo:         userRepo,
		cryptoService:    cryptoService,
	}
}

// Request types - simple, flat Go structs
type CreateLicenseRequest struct {
	PolicyID       string `json:"policyId" binding:"required"`
	OrganizationID string `json:"organizationId" binding:"required"`

	// License identification
	Name string `json:"name,omitempty"`
	Key  string `json:"key,omitempty"` // Optional - will be auto-generated if not provided

	// Owner assignment
	OwnerType string `json:"ownerType" binding:"required"` // "user" or "organization"
	OwnerID   string `json:"ownerId" binding:"required"`

	// License configuration
	Protected *bool      `json:"protected,omitempty"`
	ExpiresAt *time.Time `json:"expiresAt,omitempty"`

	// Policy overrides
	MaxUsesOverride      *int `json:"maxUsesOverride,omitempty"`
	MaxMachinesOverride  *int `json:"maxMachinesOverride,omitempty"`
	MaxCoresOverride     *int `json:"maxCoresOverride,omitempty"`
	MaxUsersOverride     *int `json:"maxUsersOverride,omitempty"`
	MaxProcessesOverride *int `json:"maxProcessesOverride,omitempty"`

	// Metadata
	Metadata map[string]any `json:"metadata,omitempty"`
}

type UpdateLicenseRequest struct {
	// License identification
	Name string `json:"name,omitempty"`

	// License configuration
	Protected *bool      `json:"protected,omitempty"`
	ExpiresAt *time.Time `json:"expiresAt,omitempty"`

	// Policy overrides
	MaxUsesOverride      *int `json:"maxUsesOverride,omitempty"`
	MaxMachinesOverride  *int `json:"maxMachinesOverride,omitempty"`
	MaxCoresOverride     *int `json:"maxCoresOverride,omitempty"`
	MaxUsersOverride     *int `json:"maxUsersOverride,omitempty"`
	MaxProcessesOverride *int `json:"maxProcessesOverride,omitempty"`

	// Metadata
	Metadata map[string]any `json:"metadata,omitempty"`
}

// Response types - simple, flat Go structs
type LicenseResponse struct {
	ID             string  `json:"id"`
	OrganizationID string  `json:"organizationId"`
	PolicyID       string  `json:"policyId"`
	ProductID      string  `json:"productId"`
	Key            string  `json:"key"`
	Name           *string `json:"name,omitempty"`

	// Owner information
	OwnerType string `json:"ownerType"`
	OwnerID   string `json:"ownerId"`

	// License state
	Status    string `json:"status"`
	Suspended bool   `json:"suspended"`
	Protected *bool  `json:"protected,omitempty"`

	// Usage tracking
	Uses      int        `json:"uses"`
	ExpiresAt *time.Time `json:"expiresAt,omitempty"`
	LastUsed  *time.Time `json:"lastUsed,omitempty"`

	// Policy overrides
	MaxUsesOverride      *int `json:"maxUsesOverride,omitempty"`
	MaxMachinesOverride  *int `json:"maxMachinesOverride,omitempty"`
	MaxCoresOverride     *int `json:"maxCoresOverride,omitempty"`
	MaxUsersOverride     *int `json:"maxUsersOverride,omitempty"`
	MaxProcessesOverride *int `json:"maxProcessesOverride,omitempty"`

	// Computed limits (from license or policy)
	MaxUses      *int `json:"maxUses,omitempty"`
	MaxMachines  *int `json:"maxMachines,omitempty"`
	MaxCores     *int `json:"maxCores,omitempty"`
	MaxUsers     *int `json:"maxUsers,omitempty"`
	MaxProcesses *int `json:"maxProcesses,omitempty"`

	// Usage counts
	MachinesCount     int `json:"machinesCount"`
	MachinesCoreCount int `json:"machinesCoreCount"`
	UsersCount        int `json:"usersCount"`

	// Check-in/out tracking
	RequiresCheckIn bool       `json:"requiresCheckIn"`
	LastCheckInAt   *time.Time `json:"lastCheckInAt,omitempty"`
	LastCheckOutAt  *time.Time `json:"lastCheckOutAt,omitempty"`
	NextCheckInAt   *time.Time `json:"nextCheckInAt,omitempty"`
	LastValidatedAt *time.Time `json:"lastValidatedAt,omitempty"`

	// Metadata and timestamps
	Metadata  map[string]any `json:"metadata,omitempty"`
	CreatedAt time.Time      `json:"createdAt"`
	UpdatedAt time.Time      `json:"updatedAt"`
}

type LicenseListResponse struct {
	Licenses []LicenseResponse `json:"licenses"`
	Total    int64             `json:"total"`
	Page     int               `json:"page"`
	Limit    int               `json:"limit"`
}

// License action requests
type RenewLicenseRequest struct {
	// Empty - renewal uses policy duration
}

type SuspendLicenseRequest struct {
	// Empty - suspension is a simple state change
}

type TransferLicenseRequest struct {
	PolicyID string `json:"policyId" binding:"required"`
}

// GetLicenses handles GET /licenses - list licenses with pagination
func (h *LicenseHandler) GetLicenses(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "25"))
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 25
	}

	// Parse filter parameters
	filter := repositories.ListFilter{
		Page:     page,
		PageSize: limit,
		Search:   c.Query("search"),
		Filters:  make(map[string]any),
	}

	// Add organization filter if provided
	if orgID := c.Query("organizationId"); orgID != "" {
		if _, err := uuid.Parse(orgID); err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INVALID_ORGANIZATION_ID",
					Message: "Invalid organization ID format",
				},
			})
			return
		}
		filter.Filters = map[string]any{"organization_id": orgID}
	}

	// Add policy filter if provided
	if policyID := c.Query("policyId"); policyID != "" {
		if _, err := uuid.Parse(policyID); err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INVALID_POLICY_ID",
					Message: "Invalid policy ID format",
				},
			})
			return
		}
		filter.Filters = map[string]any{"policy_id": policyID}
	}

	// Add status filter if provided
	if status := c.Query("status"); status != "" {
		filter.Filters = map[string]any{"status": status}
	}

	// Get licenses
	licenses, total, err := h.licenseRepo.List(c.Request.Context(), filter)
	if err != nil {
		log.Error().Err(err).Msg("Failed to list licenses")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to retrieve licenses",
			},
		})
		return
	}

	// Convert to response format
	response := LicenseListResponse{
		Licenses: make([]LicenseResponse, len(licenses)),
		Total:    total,
		Page:     page,
		Limit:    limit,
	}

	for i, license := range licenses {
		response.Licenses[i] = h.entityToResponse(license)
	}

	c.JSON(http.StatusOK, response)
}

// GetLicense handles GET /licenses/:id - get single license
func (h *LicenseHandler) GetLicense(c *gin.Context) {
	licenseID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_LICENSE_ID",
				Message: "Invalid license ID format",
			},
		})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "LICENSE_NOT_FOUND",
					Message: "License not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to fetch license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch license",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(license))
}

// CreateLicense handles POST /licenses - create new license
func (h *LicenseHandler) CreateLicense(c *gin.Context) {
	var req CreateLicenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: err.Error(),
			},
		})
		return
	}

	// Parse and validate IDs
	policyID, err := uuid.Parse(req.PolicyID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_POLICY_ID",
				Message: "Invalid policy ID format",
			},
		})
		return
	}

	organizationID, err := uuid.Parse(req.OrganizationID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_ORGANIZATION_ID",
				Message: "Invalid organization ID format",
			},
		})
		return
	}

	ownerID, err := uuid.Parse(req.OwnerID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_OWNER_ID",
				Message: "Invalid owner ID format",
			},
		})
		return
	}

	// Validate policy exists
	policy, err := h.policyRepo.GetByID(c.Request.Context(), policyID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
				Error: ErrorDetail{
					Code:    "POLICY_NOT_FOUND",
					Message: "Specified policy does not exist",
				},
			})
			return
		}
		log.Error().Err(err).Str("policy_id", policyID.String()).Msg("Failed to validate policy")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to validate policy",
			},
		})
		return
	}

	// Validate organization exists
	_, err = h.organizationRepo.GetByID(c.Request.Context(), organizationID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
				Error: ErrorDetail{
					Code:    "ORGANIZATION_NOT_FOUND",
					Message: "Specified organization does not exist",
				},
			})
			return
		}
		log.Error().Err(err).Str("organization_id", organizationID.String()).Msg("Failed to validate organization")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to validate organization",
			},
		})
		return
	}

	// Validate owner based on owner type
	ownerType := entities.LicenseOwnerType(req.OwnerType)
	if !ownerType.IsValid() {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_OWNER_TYPE",
				Message: "Owner type must be 'user' or 'organization'",
			},
		})
		return
	}

	if ownerType == entities.LicenseOwnerTypeUser {
		_, err = h.userRepo.GetByID(c.Request.Context(), ownerID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
					Error: ErrorDetail{
						Code:    "OWNER_NOT_FOUND",
						Message: "Specified user owner does not exist",
					},
				})
				return
			}
			log.Error().Err(err).Str("owner_id", ownerID.String()).Msg("Failed to validate user owner")
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INTERNAL_ERROR",
					Message: "Failed to validate user owner",
				},
			})
			return
		}
	} else if ownerType == entities.LicenseOwnerTypeOrganization {
		_, err = h.organizationRepo.GetByID(c.Request.Context(), ownerID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
					Error: ErrorDetail{
						Code:    "OWNER_NOT_FOUND",
						Message: "Specified organization owner does not exist",
					},
				})
				return
			}
			log.Error().Err(err).Str("owner_id", ownerID.String()).Msg("Failed to validate organization owner")
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INTERNAL_ERROR",
					Message: "Failed to validate organization owner",
				},
			})
			return
		}
	}

	// Create license entity
	license := h.requestToEntity(&req)
	license.PolicyID = policyID
	license.OrganizationID = organizationID
	license.ProductID = policy.ProductID
	license.OwnerType = ownerType
	license.OwnerID = ownerID

	// Generate key if not provided
	if license.Key == "" {
		generatedKey, err := h.generateLicenseKey()
		if err != nil {
			log.Error().Err(err).Msg("Failed to generate license key")
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INTERNAL_ERROR",
					Message: "Failed to generate license key",
				},
			})
			return
		}
		license.Key = generatedKey
	}

	// Set expiry from policy if not provided and policy has duration
	if license.ExpiresAt == nil && policy.Duration != nil {
		if policy.ExpiresFromCreation() {
			expiry := time.Now().Add(time.Duration(*policy.Duration) * time.Second)
			license.ExpiresAt = &expiry
		}
	}

	// Set protection from policy if not explicitly set
	if license.Protected == nil {
		protected := policy.IsProtected()
		license.Protected = &protected
	}

	// Save license
	if err := h.licenseRepo.Create(c.Request.Context(), license); err != nil {
		log.Error().Err(err).Msg("Failed to create license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to create license",
			},
		})
		return
	}

	c.JSON(http.StatusCreated, h.entityToResponse(license))
}

// UpdateLicense handles PUT /licenses/:id - update existing license
func (h *LicenseHandler) UpdateLicense(c *gin.Context) {
	licenseID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_LICENSE_ID",
				Message: "Invalid license ID format",
			},
		})
		return
	}

	var req UpdateLicenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: err.Error(),
			},
		})
		return
	}

	// Get existing license
	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "LICENSE_NOT_FOUND",
					Message: "License not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to fetch license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch license",
			},
		})
		return
	}

	// Update license with request data
	h.updateEntityFromRequest(license, &req)
	license.UpdatedAt = time.Now()

	// Save updated license
	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to update license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to update license",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(license))
}

// DeleteLicense handles DELETE /licenses/:id - delete license
func (h *LicenseHandler) DeleteLicense(c *gin.Context) {
	licenseID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_LICENSE_ID",
				Message: "Invalid license ID format",
			},
		})
		return
	}

	// Check if license exists
	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "LICENSE_NOT_FOUND",
					Message: "License not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to fetch license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch license",
			},
		})
		return
	}

	// Check if license is protected
	if license.IsProtected() {
		c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
			Error: ErrorDetail{
				Code:    "LICENSE_PROTECTED",
				Message: "Cannot delete protected license",
			},
		})
		return
	}

	// Soft delete the license
	if err := h.licenseRepo.SoftDelete(c.Request.Context(), licenseID); err != nil {
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to delete license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to delete license",
			},
		})
		return
	}

	c.JSON(http.StatusNoContent, nil)
}

// RenewLicense handles POST /licenses/:id/actions/renew - renew license
func (h *LicenseHandler) RenewLicense(c *gin.Context) {
	licenseID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_LICENSE_ID",
				Message: "Invalid license ID format",
			},
		})
		return
	}

	// Get license
	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "LICENSE_NOT_FOUND",
					Message: "License not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to fetch license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch license",
			},
		})
		return
	}

	// Check if license can be renewed
	if !license.CanRenew() {
		c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
			Error: ErrorDetail{
				Code:    "LICENSE_NOT_RENEWABLE",
				Message: "License cannot be renewed",
			},
		})
		return
	}

	// Renew license
	license.Renew()

	// Save updated license
	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to renew license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to renew license",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(license))
}

// SuspendLicense handles POST /licenses/:id/actions/suspend - suspend license
func (h *LicenseHandler) SuspendLicense(c *gin.Context) {
	licenseID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_LICENSE_ID",
				Message: "Invalid license ID format",
			},
		})
		return
	}

	// Get license
	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "LICENSE_NOT_FOUND",
					Message: "License not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to fetch license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch license",
			},
		})
		return
	}

	// Suspend license
	license.Suspend()

	// Save updated license
	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to suspend license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to suspend license",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(license))
}

// ReinstateLicense handles POST /licenses/:id/actions/reinstate - reinstate suspended license
func (h *LicenseHandler) ReinstateLicense(c *gin.Context) {
	licenseID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_LICENSE_ID",
				Message: "Invalid license ID format",
			},
		})
		return
	}

	// Get license
	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "LICENSE_NOT_FOUND",
					Message: "License not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to fetch license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch license",
			},
		})
		return
	}

	// Reinstate license
	license.Reinstate()

	// Save updated license
	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to reinstate license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to reinstate license",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(license))
}

// TransferLicense handles POST /licenses/:id/actions/transfer - transfer license to new policy
func (h *LicenseHandler) TransferLicense(c *gin.Context) {
	licenseID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_LICENSE_ID",
				Message: "Invalid license ID format",
			},
		})
		return
	}

	var req TransferLicenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: err.Error(),
			},
		})
		return
	}

	// Parse new policy ID
	newPolicyID, err := uuid.Parse(req.PolicyID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_POLICY_ID",
				Message: "Invalid policy ID format",
			},
		})
		return
	}

	// Get license
	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "LICENSE_NOT_FOUND",
					Message: "License not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to fetch license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch license",
			},
		})
		return
	}

	// Validate new policy exists
	newPolicy, err := h.policyRepo.GetByID(c.Request.Context(), newPolicyID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
				Error: ErrorDetail{
					Code:    "POLICY_NOT_FOUND",
					Message: "Specified policy does not exist",
				},
			})
			return
		}
		log.Error().Err(err).Str("policy_id", newPolicyID.String()).Msg("Failed to validate new policy")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to validate new policy",
			},
		})
		return
	}

	// Transfer license
	h.transferLicense(license, newPolicy)

	// Save updated license
	if err := h.licenseRepo.Update(c.Request.Context(), license); err != nil {
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to save transferred license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to save transferred license",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(license))
}

// Helper methods for converting between entities and requests/responses

// entityToResponse converts License entity to LicenseResponse
func (h *LicenseHandler) entityToResponse(license *entities.License) LicenseResponse {
	response := LicenseResponse{
		ID:             license.ID.String(),
		OrganizationID: license.OrganizationID.String(),
		PolicyID:       license.PolicyID.String(),
		ProductID:      license.ProductID.String(),
		Key:            license.Key,
		Name:           license.Name,
		OwnerType:      string(license.OwnerType),
		OwnerID:        license.OwnerID.String(),
		Status:         string(license.Status),
		Suspended:      license.Suspended,
		Protected:      license.Protected,
		Uses:           license.Uses,
		ExpiresAt:      license.ExpiresAt,
		LastUsed:       license.LastUsed,

		// Policy overrides
		MaxUsesOverride:      license.MaxUsesOverride,
		MaxMachinesOverride:  license.MaxMachinesOverride,
		MaxCoresOverride:     license.MaxCoresOverride,
		MaxUsersOverride:     license.MaxUsersOverride,
		MaxProcessesOverride: license.MaxProcessesOverride,

		// Usage counts
		MachinesCount:     license.MachinesCount,
		MachinesCoreCount: license.MachinesCoreCount,
		UsersCount:        license.GetUsersCount(),

		// Check-in/out tracking
		RequiresCheckIn: license.RequiresCheckIn(),
		LastCheckInAt:   license.LastCheckInAt,
		LastCheckOutAt:  license.LastCheckOutAt,
		NextCheckInAt:   license.GetNextCheckInAt(),
		LastValidatedAt: license.LastValidatedAt,

		// Metadata and timestamps
		Metadata:  license.Metadata,
		CreatedAt: license.CreatedAt,
		UpdatedAt: license.UpdatedAt,
	}

	// Set computed limits (from license overrides or policy defaults)
	if maxUses := license.GetMaxUses(); maxUses != nil {
		response.MaxUses = maxUses
	}
	if maxMachines := license.GetMaxMachines(); maxMachines != nil {
		response.MaxMachines = maxMachines
	}
	if maxCores := license.GetMaxCores(); maxCores != nil {
		response.MaxCores = maxCores
	}
	if maxUsers := license.GetMaxUsers(); maxUsers != nil {
		response.MaxUsers = maxUsers
	}
	if maxProcesses := license.GetMaxProcesses(); maxProcesses != nil {
		response.MaxProcesses = maxProcesses
	}

	return response
}

// requestToEntity converts CreateLicenseRequest to License entity
func (h *LicenseHandler) requestToEntity(req *CreateLicenseRequest) *entities.License {
	license := &entities.License{
		Key:                  req.Key,
		Name:                 &req.Name,
		Protected:            req.Protected,
		ExpiresAt:            req.ExpiresAt,
		MaxUsesOverride:      req.MaxUsesOverride,
		MaxMachinesOverride:  req.MaxMachinesOverride,
		MaxCoresOverride:     req.MaxCoresOverride,
		MaxUsersOverride:     req.MaxUsersOverride,
		MaxProcessesOverride: req.MaxProcessesOverride,
		Status:               entities.LicenseStatusActive,
		Suspended:            false,
		Uses:                 0,
		Metadata:             req.Metadata,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	// Set empty name to nil if not provided
	if req.Name == "" {
		license.Name = nil
	}

	// Initialize metadata if nil
	if license.Metadata == nil {
		license.Metadata = make(map[string]any)
	}

	return license
}

// updateEntityFromRequest updates License entity with UpdateLicenseRequest data
func (h *LicenseHandler) updateEntityFromRequest(license *entities.License, req *UpdateLicenseRequest) {
	// Update name
	if req.Name != "" {
		license.Name = &req.Name
	}

	// Update protection
	if req.Protected != nil {
		license.Protected = req.Protected
	}

	// Update expiry
	if req.ExpiresAt != nil {
		license.ExpiresAt = req.ExpiresAt
	}

	// Update policy overrides
	if req.MaxUsesOverride != nil {
		license.MaxUsesOverride = req.MaxUsesOverride
	}
	if req.MaxMachinesOverride != nil {
		license.MaxMachinesOverride = req.MaxMachinesOverride
	}
	if req.MaxCoresOverride != nil {
		license.MaxCoresOverride = req.MaxCoresOverride
	}
	if req.MaxUsersOverride != nil {
		license.MaxUsersOverride = req.MaxUsersOverride
	}
	if req.MaxProcessesOverride != nil {
		license.MaxProcessesOverride = req.MaxProcessesOverride
	}

	// Update metadata
	if req.Metadata != nil {
		license.Metadata = req.Metadata
	}
}

// generateLicenseKey generates a simple license key in the format XXXX-XXXX-XXXX-XXXX
func (h *LicenseHandler) generateLicenseKey() (string, error) {
	// Generate 16 random bytes
	bytes := make([]byte, 16)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random bytes: %w", err)
	}

	// Convert to hex string
	hexStr := hex.EncodeToString(bytes)

	// Format as XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX-XXXX
	var parts []string
	for i := 0; i < len(hexStr); i += 4 {
		end := i + 4
		if end > len(hexStr) {
			end = len(hexStr)
		}
		parts = append(parts, strings.ToUpper(hexStr[i:end]))
	}

	return strings.Join(parts, "-"), nil
}

// transferLicense transfers a license to a new policy (Ruby: transfer! method)
func (h *LicenseHandler) transferLicense(license *entities.License, newPolicy *entities.Policy) {
	// Update policy reference
	license.PolicyID = newPolicy.ID
	license.ProductID = newPolicy.ProductID

	// Reset expiry if policy requires it
	if newPolicy.ResetsExpiryOnTransfer() {
		if newPolicy.Duration != nil {
			expiry := time.Now().Add(time.Duration(*newPolicy.Duration) * time.Second)
			license.ExpiresAt = &expiry
		} else {
			license.ExpiresAt = nil
		}
	}

	license.UpdatedAt = time.Now()
}
