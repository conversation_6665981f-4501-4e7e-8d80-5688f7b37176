package handlers

import (
	"fmt"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/adapters/http/handlers/common"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// RefactoredOrganizationHandler demonstrates the new refactored handler pattern
type RefactoredOrganizationHandler struct {
	*common.CRUDHandler[entities.Organization, CreateOrganizationRequestRefactored, UpdateOrganizationRequestRefactored, OrganizationResponseRefactored]
	organizationRepo repositories.OrganizationRepository
	errorHandler     *common.ErrorHandler
	httpUtils        *common.HTTPUtils
	validator        *common.Validator
}

// Request types - simple, flat Go structs
type CreateOrganizationRequestRefactored struct {
	Name        string                        `json:"name" binding:"required"`
	Slug        string                        `json:"slug" binding:"required"`
	Email       string                        `json:"email" binding:"required,email"`
	Type        string                        `json:"type,omitempty"`
	Status      string                        `json:"status,omitempty"`
	Protected   bool                          `json:"protected"`
	MaxUsers    *int                          `json:"maxUsers,omitempty"`
	MaxLicenses *int                          `json:"maxLicenses,omitempty"`
	MaxMachines *int                          `json:"maxMachines,omitempty"`
	Settings    entities.OrganizationSettings `json:"settings,omitempty"`
	Metadata    entities.Metadata             `json:"metadata,omitempty"`
}

// Validate implements the Validatable interface
func (r *CreateOrganizationRequestRefactored) Validate() error {
	validator := common.NewValidator()

	if err := validator.ValidateRequired(r.Name, "name"); err != nil {
		return err
	}
	if err := validator.ValidateLength(r.Name, "name", 1, 255); err != nil {
		return err
	}

	if err := validator.ValidateSlug(r.Slug); err != nil {
		return err
	}

	if err := validator.ValidateEmail(r.Email); err != nil {
		return err
	}

	// Validate type if provided
	if r.Type != "" {
		validTypes := []string{
			entities.OrganizationTypeVendor,
			entities.OrganizationTypeReseller,
			entities.OrganizationTypeCustomer,
		}
		if err := validator.ValidateEnum(r.Type, "type", validTypes); err != nil {
			return err
		}
	}

	// Validate status if provided
	if r.Status != "" {
		validStatuses := []string{
			entities.OrganizationStatusActive,
			entities.OrganizationStatusSuspended,
			entities.OrganizationStatusCanceled,
		}
		if err := validator.ValidateEnum(r.Status, "status", validStatuses); err != nil {
			return err
		}
	}

	// Validate limits
	if err := validator.ValidatePositiveInt(r.MaxUsers, "maxUsers"); err != nil {
		return err
	}
	if err := validator.ValidatePositiveInt(r.MaxLicenses, "maxLicenses"); err != nil {
		return err
	}
	if err := validator.ValidatePositiveInt(r.MaxMachines, "maxMachines"); err != nil {
		return err
	}

	return nil
}

type UpdateOrganizationRequestRefactored struct {
	Name        *string                       `json:"name,omitempty"`
	Email       *string                       `json:"email,omitempty"`
	Type        *string                       `json:"type,omitempty"`
	Status      *string                       `json:"status,omitempty"`
	Protected   *bool                         `json:"protected,omitempty"`
	MaxUsers    *int                          `json:"maxUsers,omitempty"`
	MaxLicenses *int                          `json:"maxLicenses,omitempty"`
	MaxMachines *int                          `json:"maxMachines,omitempty"`
	Settings    entities.OrganizationSettings `json:"settings,omitempty"`
	Metadata    entities.Metadata             `json:"metadata,omitempty"`
}

// Validate implements the Validatable interface
func (r *UpdateOrganizationRequestRefactored) Validate() error {
	validator := common.NewValidator()

	if r.Name != nil {
		if err := validator.ValidateRequired(*r.Name, "name"); err != nil {
			return err
		}
		if err := validator.ValidateLength(*r.Name, "name", 1, 255); err != nil {
			return err
		}
	}

	if r.Email != nil {
		if err := validator.ValidateEmail(*r.Email); err != nil {
			return err
		}
	}

	if r.Type != nil {
		validTypes := []string{
			entities.OrganizationTypeVendor,
			entities.OrganizationTypeReseller,
			entities.OrganizationTypeCustomer,
		}
		if err := validator.ValidateEnum(*r.Type, "type", validTypes); err != nil {
			return err
		}
	}

	if r.Status != nil {
		validStatuses := []string{
			entities.OrganizationStatusActive,
			entities.OrganizationStatusSuspended,
			entities.OrganizationStatusCanceled,
		}
		if err := validator.ValidateEnum(*r.Status, "status", validStatuses); err != nil {
			return err
		}
	}

	// Validate limits
	if err := validator.ValidatePositiveInt(r.MaxUsers, "maxUsers"); err != nil {
		return err
	}
	if err := validator.ValidatePositiveInt(r.MaxLicenses, "maxLicenses"); err != nil {
		return err
	}
	if err := validator.ValidatePositiveInt(r.MaxMachines, "maxMachines"); err != nil {
		return err
	}

	return nil
}

// Response types - simple, flat Go structs
type OrganizationResponseRefactored struct {
	ID          string                        `json:"id"`
	Name        string                        `json:"name"`
	Slug        string                        `json:"slug"`
	Email       string                        `json:"email"`
	Type        string                        `json:"type"`
	Status      string                        `json:"status"`
	Protected   bool                          `json:"protected"`
	MaxUsers    *int                          `json:"maxUsers,omitempty"`
	MaxLicenses *int                          `json:"maxLicenses,omitempty"`
	MaxMachines *int                          `json:"maxMachines,omitempty"`
	Settings    entities.OrganizationSettings `json:"settings"`
	Metadata    entities.Metadata             `json:"metadata"`
	CreatedAt   time.Time                     `json:"createdAt"`
	UpdatedAt   time.Time                     `json:"updatedAt"`
}

// OrganizationTransformer implements the request/response transformation interfaces
type OrganizationTransformer struct{}

// CreateRequestToEntity transforms create request to entity
func (t *OrganizationTransformer) CreateRequestToEntity(req *CreateOrganizationRequestRefactored) *entities.Organization {
	organization := &entities.Organization{
		ID:        uuid.New(),
		Name:      req.Name,
		Slug:      req.Slug,
		Email:     req.Email,
		Protected: req.Protected,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Set type with default
	if req.Type != "" {
		organization.Type = req.Type
	} else {
		organization.Type = entities.OrganizationTypeVendor
	}

	// Set status with default
	if req.Status != "" {
		organization.Status = req.Status
	} else {
		organization.Status = entities.OrganizationStatusActive
	}

	// Set optional fields
	organization.MaxUsers = req.MaxUsers
	organization.MaxLicenses = req.MaxLicenses
	organization.MaxMachines = req.MaxMachines

	// Set settings and metadata with defaults
	if req.Settings != nil {
		organization.Settings = req.Settings
	} else {
		organization.Settings = make(entities.OrganizationSettings)
	}

	if req.Metadata != nil {
		organization.Metadata = req.Metadata
	} else {
		organization.Metadata = make(entities.Metadata)
	}

	return organization
}

// UpdateRequestToEntity transforms update request to entity
func (t *OrganizationTransformer) UpdateRequestToEntity(req *UpdateOrganizationRequestRefactored, existing *entities.Organization) *entities.Organization {
	// Start with existing entity
	updated := *existing
	updated.UpdatedAt = time.Now()

	// Apply updates
	if req.Name != nil {
		updated.Name = *req.Name
	}
	if req.Email != nil {
		updated.Email = *req.Email
	}
	if req.Type != nil {
		updated.Type = *req.Type
	}
	if req.Status != nil {
		updated.Status = *req.Status
	}
	if req.Protected != nil {
		updated.Protected = *req.Protected
	}
	if req.MaxUsers != nil {
		updated.MaxUsers = req.MaxUsers
	}
	if req.MaxLicenses != nil {
		updated.MaxLicenses = req.MaxLicenses
	}
	if req.MaxMachines != nil {
		updated.MaxMachines = req.MaxMachines
	}
	if req.Settings != nil {
		updated.Settings = req.Settings
	}
	if req.Metadata != nil {
		updated.Metadata = req.Metadata
	}

	return &updated
}

// EntityToResponse transforms entity to response
func (t *OrganizationTransformer) EntityToResponse(entity *entities.Organization) OrganizationResponseRefactored {
	return OrganizationResponseRefactored{
		ID:          entity.ID.String(),
		Name:        entity.Name,
		Slug:        entity.Slug,
		Email:       entity.Email,
		Type:        entity.Type,
		Status:      entity.Status,
		Protected:   entity.Protected,
		MaxUsers:    entity.MaxUsers,
		MaxLicenses: entity.MaxLicenses,
		MaxMachines: entity.MaxMachines,
		Settings:    entity.Settings,
		Metadata:    entity.Metadata,
		CreatedAt:   entity.CreatedAt,
		UpdatedAt:   entity.UpdatedAt,
	}
}

// NewRefactoredOrganizationHandler creates a new refactored organization handler
func NewRefactoredOrganizationHandler(organizationRepo repositories.OrganizationRepository) *RefactoredOrganizationHandler {
	transformer := &OrganizationTransformer{}

	// Create the generic CRUD handler
	crudHandler := common.NewCRUDHandler[entities.Organization, CreateOrganizationRequestRefactored, UpdateOrganizationRequestRefactored, OrganizationResponseRefactored](
		organizationRepo,
		transformer,
		transformer,
		"organization",
	)

	return &RefactoredOrganizationHandler{
		CRUDHandler:      crudHandler,
		organizationRepo: organizationRepo,
		errorHandler:     common.NewErrorHandler(),
		httpUtils:        common.NewHTTPUtils(),
		validator:        common.NewValidator(),
	}
}

// GetOrganizations handles GET /organizations - uses the generic List method
func (h *RefactoredOrganizationHandler) GetOrganizations(c *gin.Context) {
	h.List(c)
}

// GetOrganization handles GET /organizations/{id} - uses the generic GetByID method
func (h *RefactoredOrganizationHandler) GetOrganization(c *gin.Context) {
	h.GetByID(c)
}

// CreateOrganization handles POST /organizations - uses the generic Create method
func (h *RefactoredOrganizationHandler) CreateOrganization(c *gin.Context) {
	h.Create(c)
}

// UpdateOrganization handles PUT /organizations/{id} - uses the generic Update method
func (h *RefactoredOrganizationHandler) UpdateOrganization(c *gin.Context) {
	h.Update(c)
}

// DeleteOrganization handles DELETE /organizations/{id} - uses the generic Delete method
func (h *RefactoredOrganizationHandler) DeleteOrganization(c *gin.Context) {
	h.Delete(c)
}

// GetOrganizationBySlug handles GET /organizations/by-slug/{slug} - custom method
func (h *RefactoredOrganizationHandler) GetOrganizationBySlug(c *gin.Context) {
	slug := c.Param("slug")
	if slug == "" {
		h.errorHandler.HandleValidationError(c, fmt.Errorf("slug parameter is required"))
		return
	}

	// Validate slug format
	if err := h.validator.ValidateSlug(slug); err != nil {
		h.errorHandler.HandleValidationError(c, err)
		return
	}

	// Fetch organization by slug
	organization, err := h.organizationRepo.GetBySlug(c.Request.Context(), slug)
	if h.errorHandler.HandleRepositoryError(c, err, "organization", "fetch") {
		return
	}

	// Transform to response
	transformer := &OrganizationTransformer{}
	response := transformer.EntityToResponse(organization)
	h.errorHandler.Success(c, response)
}
