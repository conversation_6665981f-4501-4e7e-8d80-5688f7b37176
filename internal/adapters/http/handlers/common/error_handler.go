package common

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// ErrorResponse represents the standard error response format
type ErrorResponse struct {
	Error ErrorDetail `json:"error"`
}

// ErrorDetail contains error code and message
type ErrorDetail struct {
	Code    string `json:"code"`
	Message string `json:"message"`
}

// ErrorHandler provides centralized error handling for HTTP handlers
type ErrorHandler struct{}

// NewErrorHandler creates a new error handler instance
func NewErrorHandler() *ErrorHandler {
	return &ErrorHandler{}
}

// HandleRepositoryError handles common repository errors with consistent responses
func (e *ErrorHandler) HandleRepositoryError(c *gin.Context, err error, entityName string, operation string) bool {
	if err == nil {
		return false
	}

	// Convert entity name to uppercase for error codes
	entityCode := strings.ToUpper(entityName)

	if err == gorm.ErrRecordNotFound {
		c.JSON(http.StatusNotFound, ErrorResponse{
			Error: ErrorDetail{
				Code:    entityCode + "_NOT_FOUND",
				Message: entityName + " not found",
			},
		})
		return true
	}

	// Check for unique constraint violations
	if strings.Contains(err.Error(), "duplicate key") || strings.Contains(err.Error(), "already exists") {
		var code, message string
		if strings.Contains(err.Error(), "slug") {
			code = entityCode + "_SLUG_EXISTS"
			message = entityName + " with this slug already exists"
		} else if strings.Contains(err.Error(), "email") {
			code = entityCode + "_EMAIL_EXISTS"
			message = entityName + " with this email already exists"
		} else {
			code = entityCode + "_ALREADY_EXISTS"
			message = entityName + " already exists"
		}

		c.JSON(http.StatusConflict, ErrorResponse{
			Error: ErrorDetail{
				Code:    code,
				Message: message,
			},
		})
		return true
	}

	// Log the error for debugging
	log.Error().
		Err(err).
		Str("entity", entityName).
		Str("operation", operation).
		Msg("Repository operation failed")

	// Return generic internal error
	c.JSON(http.StatusInternalServerError, ErrorResponse{
		Error: ErrorDetail{
			Code:    "INTERNAL_ERROR",
			Message: "Failed to " + operation + " " + entityName,
		},
	})
	return true
}

// HandleValidationError handles request validation errors
func (e *ErrorHandler) HandleValidationError(c *gin.Context, err error) {
	log.Warn().Err(err).Msg("Validation error")
	c.JSON(http.StatusBadRequest, ErrorResponse{
		Error: ErrorDetail{
			Code:    "VALIDATION_ERROR",
			Message: err.Error(),
		},
	})
}

// HandleBusinessLogicError handles business logic errors with custom status codes
func (e *ErrorHandler) HandleBusinessLogicError(c *gin.Context, code string, message string, statusCode int) {
	log.Warn().
		Str("code", code).
		Str("message", message).
		Int("status", statusCode).
		Msg("Business logic error")

	c.JSON(statusCode, ErrorResponse{
		Error: ErrorDetail{
			Code:    code,
			Message: message,
		},
	})
}

// HandleProtectedEntityError handles attempts to modify protected entities
func (e *ErrorHandler) HandleProtectedEntityError(c *gin.Context, entityName string) {
	entityCode := strings.ToUpper(entityName)
	c.JSON(http.StatusForbidden, ErrorResponse{
		Error: ErrorDetail{
			Code:    entityCode + "_PROTECTED",
			Message: "Cannot modify protected " + entityName,
		},
	})
}

// HandleUnauthorizedError handles unauthorized access attempts
func (e *ErrorHandler) HandleUnauthorizedError(c *gin.Context, message string) {
	if message == "" {
		message = "Unauthorized access"
	}
	c.JSON(http.StatusUnauthorized, ErrorResponse{
		Error: ErrorDetail{
			Code:    "UNAUTHORIZED",
			Message: message,
		},
	})
}

// HandleForbiddenError handles forbidden access attempts
func (e *ErrorHandler) HandleForbiddenError(c *gin.Context, message string) {
	if message == "" {
		message = "Access forbidden"
	}
	c.JSON(http.StatusForbidden, ErrorResponse{
		Error: ErrorDetail{
			Code:    "FORBIDDEN",
			Message: message,
		},
	})
}

// Success sends a successful response with data
func (e *ErrorHandler) Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, data)
}

// Created sends a successful creation response
func (e *ErrorHandler) Created(c *gin.Context, data interface{}) {
	c.JSON(http.StatusCreated, data)
}

// NoContent sends a successful no-content response
func (e *ErrorHandler) NoContent(c *gin.Context) {
	c.Status(http.StatusNoContent)
}
