package common

import (
	"fmt"
	"regexp"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// Validator provides common validation utilities
type Validator struct{}

// NewValidator creates a new validator instance
func NewValidator() *Validator {
	return &Validator{}
}

var (
	emailRegex = regexp.MustCompile(`^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$`)
	slugRegex  = regexp.MustCompile(`^[a-z0-9-]+$`)
)

// ValidateUUID validates a UUID string
func (v *Validator) ValidateUUID(id string, fieldName string) error {
	if id == "" {
		return fmt.Errorf("%s is required", fieldName)
	}
	if _, err := uuid.Parse(id); err != nil {
		return fmt.Errorf("invalid %s format: must be a valid UUID", fieldName)
	}
	return nil
}

// ValidateEmail validates an email address
func (v *Validator) ValidateEmail(email string) error {
	if email == "" {
		return fmt.Errorf("email is required")
	}
	if !emailRegex.MatchString(email) {
		return fmt.Errorf("invalid email format")
	}
	return nil
}

// ValidateSlug validates a slug (URL-friendly identifier)
func (v *Validator) ValidateSlug(slug string) error {
	if slug == "" {
		return fmt.Errorf("slug is required")
	}
	if len(slug) < 3 || len(slug) > 50 {
		return fmt.Errorf("slug must be between 3 and 50 characters")
	}
	if !slugRegex.MatchString(slug) {
		return fmt.Errorf("slug can only contain lowercase letters, numbers, and hyphens")
	}
	return nil
}

// ValidateRequired validates that a string field is not empty
func (v *Validator) ValidateRequired(value string, fieldName string) error {
	if strings.TrimSpace(value) == "" {
		return fmt.Errorf("%s is required", fieldName)
	}
	return nil
}

// ValidateLength validates string length constraints
func (v *Validator) ValidateLength(value string, fieldName string, min, max int) error {
	length := len(strings.TrimSpace(value))
	if length < min {
		return fmt.Errorf("%s must be at least %d characters", fieldName, min)
	}
	if length > max {
		return fmt.Errorf("%s must be at most %d characters", fieldName, max)
	}
	return nil
}

// ValidateEnum validates that a value is in a list of valid options
func (v *Validator) ValidateEnum(value string, fieldName string, validValues []string) error {
	for _, valid := range validValues {
		if value == valid {
			return nil
		}
	}
	return fmt.Errorf("%s must be one of: %s", fieldName, strings.Join(validValues, ", "))
}

// ValidatePositiveInt validates that an integer is positive
func (v *Validator) ValidatePositiveInt(value *int, fieldName string) error {
	if value != nil && *value <= 0 {
		return fmt.Errorf("%s must be a positive integer", fieldName)
	}
	return nil
}

// ValidateRange validates that an integer is within a specific range
func (v *Validator) ValidateRange(value *int, fieldName string, min, max int) error {
	if value != nil {
		if *value < min {
			return fmt.Errorf("%s must be at least %d", fieldName, min)
		}
		if *value > max {
			return fmt.Errorf("%s must be at most %d", fieldName, max)
		}
	}
	return nil
}

// ValidateUUIDParam creates middleware to validate UUID parameters
func ValidateUUIDParam(paramName string) gin.HandlerFunc {
	return func(c *gin.Context) {
		idStr := c.Param(paramName)
		if idStr == "" {
			errorHandler := NewErrorHandler()
			errorHandler.HandleValidationError(c, fmt.Errorf("%s parameter is required", paramName))
			c.Abort()
			return
		}

		if _, err := uuid.Parse(idStr); err != nil {
			errorHandler := NewErrorHandler()
			errorHandler.HandleValidationError(c, fmt.Errorf("invalid %s format: must be a valid UUID", paramName))
			c.Abort()
			return
		}

		c.Next()
	}
}

// ValidateContentType creates middleware to validate request content type
func ValidateContentType(expectedType string) gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			contentType := c.GetHeader("Content-Type")
			if !strings.Contains(contentType, expectedType) {
				errorHandler := NewErrorHandler()
				errorHandler.HandleValidationError(c, fmt.Errorf("invalid content type: expected %s", expectedType))
				c.Abort()
				return
			}
		}
		c.Next()
	}
}

// ValidatePagination creates middleware to validate pagination parameters
func ValidatePagination() gin.HandlerFunc {
	return func(c *gin.Context) {
		httpUtils := NewHTTPUtils()
		
		// This will set defaults if invalid values are provided
		page, limit := httpUtils.ParsePagination(c)
		
		// Store validated values in context for handlers to use
		c.Set("validated_page", page)
		c.Set("validated_limit", limit)
		
		c.Next()
	}
}

// Validatable interface for request objects that can validate themselves
type Validatable interface {
	Validate() error
}

// ValidateRequest creates middleware to validate request bodies
func ValidateRequest[T Validatable]() gin.HandlerFunc {
	return func(c *gin.Context) {
		var req T
		
		if err := c.ShouldBindJSON(&req); err != nil {
			errorHandler := NewErrorHandler()
			errorHandler.HandleValidationError(c, fmt.Errorf("invalid request format: %v", err))
			c.Abort()
			return
		}

		if err := req.Validate(); err != nil {
			errorHandler := NewErrorHandler()
			errorHandler.HandleValidationError(c, err)
			c.Abort()
			return
		}

		// Store validated request in context
		c.Set("validated_request", req)
		c.Next()
	}
}

// GetValidatedRequest retrieves a validated request from context
func GetValidatedRequest[T any](c *gin.Context) (T, bool) {
	if req, exists := c.Get("validated_request"); exists {
		if typedReq, ok := req.(T); ok {
			return typedReq, true
		}
	}
	var zero T
	return zero, false
}

// GetValidatedPagination retrieves validated pagination from context
func GetValidatedPagination(c *gin.Context) (page int, limit int) {
	if p, exists := c.Get("validated_page"); exists {
		page = p.(int)
	} else {
		page = 1
	}
	
	if l, exists := c.Get("validated_limit"); exists {
		limit = l.(int)
	} else {
		limit = 20
	}
	
	return page, limit
}
