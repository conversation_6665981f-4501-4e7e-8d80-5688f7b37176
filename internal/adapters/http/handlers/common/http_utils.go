package common

import (
	"fmt"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
)

// HTTPUtils provides common HTTP utilities for handlers
type HTTPUtils struct {
	errorHandler *ErrorHandler
}

// NewHTTPUtils creates a new HTTP utilities instance
func NewHTTPUtils() *HTTPUtils {
	return &HTTPUtils{
		errorHandler: NewErrorHandler(),
	}
}

// ParseUUIDParam extracts and validates a UUID parameter from the request
func (h *HTTPUtils) ParseUUIDParam(c *gin.Context, paramName string) (uuid.UUID, bool) {
	idStr := c.Param(paramName)
	if idStr == "" {
		h.errorHandler.HandleValidationError(c, fmt.Errorf("%s parameter is required", paramName))
		return uuid.Nil, false
	}

	id, err := uuid.Parse(idStr)
	if err != nil {
		h.errorHandler.HandleValidationError(c, fmt.Errorf("invalid %s format: must be a valid UUID", paramName))
		return uuid.Nil, false
	}

	return id, true
}

// ParsePagination extracts pagination parameters with validation and defaults
func (h *HTTPUtils) ParsePagination(c *gin.Context) (page int, limit int) {
	// Parse page with default
	page, err := strconv.Atoi(c.DefaultQuery("page", "1"))
	if err != nil || page < 1 {
		page = 1
	}

	// Parse limit with default and maximum
	limit, err = strconv.Atoi(c.DefaultQuery("limit", "20"))
	if err != nil || limit < 1 {
		limit = 20
	}
	if limit > 100 {
		limit = 100
	}

	return page, limit
}

// BindAndValidate binds JSON request and validates it
func (h *HTTPUtils) BindAndValidate(c *gin.Context, req interface{}) bool {
	if err := c.ShouldBindJSON(req); err != nil {
		h.errorHandler.HandleValidationError(c, fmt.Errorf("invalid request format: %v", err))
		return false
	}

	// If the request implements a Validate method, call it
	if validator, ok := req.(interface{ Validate() error }); ok {
		if err := validator.Validate(); err != nil {
			h.errorHandler.HandleValidationError(c, err)
			return false
		}
	}

	return true
}

// GetSearchQuery extracts search query parameter
func (h *HTTPUtils) GetSearchQuery(c *gin.Context) string {
	return c.Query("search")
}

// GetSortParams extracts sorting parameters
func (h *HTTPUtils) GetSortParams(c *gin.Context) (sortBy string, sortDir string) {
	sortBy = c.DefaultQuery("sort_by", "created_at")
	sortDir = c.DefaultQuery("sort_dir", "desc")

	// Validate sort direction
	if sortDir != "asc" && sortDir != "desc" {
		sortDir = "desc"
	}

	return sortBy, sortDir
}

// BuildListFilter creates a standardized list filter from query parameters
func (h *HTTPUtils) BuildListFilter(c *gin.Context) ListFilter {
	page, limit := h.ParsePagination(c)
	sortBy, sortDir := h.GetSortParams(c)
	search := h.GetSearchQuery(c)

	return ListFilter{
		Page:     page,
		PageSize: limit,
		Search:   search,
		SortBy:   sortBy,
		SortDir:  sortDir,
	}
}

// ListFilter represents common filtering and pagination parameters
type ListFilter struct {
	Page     int    `json:"page"`
	PageSize int    `json:"page_size"`
	Search   string `json:"search,omitempty"`
	SortBy   string `json:"sort_by,omitempty"`
	SortDir  string `json:"sort_dir,omitempty"`
}

// ListResponse represents a standardized paginated response
type ListResponse[T any] struct {
	Data  []T   `json:"data"`
	Total int64 `json:"total"`
	Page  int   `json:"page"`
	Limit int   `json:"limit"`
	Pages int64 `json:"pages"`
}

// NewListResponse creates a new paginated list response
func NewListResponse[T any](data []T, total int64, page, limit int) *ListResponse[T] {
	pages := (total + int64(limit) - 1) / int64(limit)
	if pages < 1 {
		pages = 1
	}

	return &ListResponse[T]{
		Data:  data,
		Total: total,
		Page:  page,
		Limit: limit,
		Pages: pages,
	}
}

// ResponseBuilder provides utilities for building consistent responses
type ResponseBuilder struct{}

// NewResponseBuilder creates a new response builder
func NewResponseBuilder() *ResponseBuilder {
	return &ResponseBuilder{}
}

// BuildListResponse builds a standardized list response
func BuildListResponse[T any](items []T, total int64, page, limit int) *ListResponse[T] {
	return NewListResponse(items, total, page, limit)
}

// ValidationMiddleware adds request validation and tracing
func ValidationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add request ID for tracing
		requestID := uuid.New().String()
		c.Set("request_id", requestID)
		c.Header("X-Request-ID", requestID)
		c.Next()
	}
}

// ErrorHandlingMiddleware provides centralized panic recovery
func ErrorHandlingMiddleware() gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		errorHandler := NewErrorHandler()

		if err, ok := recovered.(string); ok {
			log.Error().Str("error", err).Msg("Panic recovered")
		} else {
			log.Error().Interface("error", recovered).Msg("Panic recovered")
		}

		errorHandler.HandleBusinessLogicError(c, "INTERNAL_ERROR", "Internal server error", 500)
		c.Abort()
	})
}

// CORSMiddleware adds CORS headers
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization, X-Request-ID")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}
