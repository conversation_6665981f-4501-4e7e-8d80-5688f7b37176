package common

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
)

// Repository interface defines the basic repository operations
type Repository[T any] interface {
	Create(ctx context.Context, entity *T) error
	GetByID(ctx context.Context, id uuid.UUID) (*T, error)
	Update(ctx context.Context, entity *T) error
	Delete(ctx context.Context, id uuid.UUID) error
	SoftDelete(ctx context.Context, id uuid.UUID) error
	List(ctx context.Context, filter repositories.ListFilter) ([]*T, int64, error)
	Count(ctx context.Context, filter repositories.ListFilter) (int64, error)
	Exists(ctx context.Context, id uuid.UUID) (bool, error)
}

// RequestTransformer defines how to transform requests to entities
type RequestTransformer[CreateReq any, UpdateReq any, Entity any] interface {
	CreateRequestToEntity(req *CreateReq) *Entity
	UpdateRequestToEntity(req *UpdateReq, existing *Entity) *Entity
}

// ResponseTransformer defines how to transform entities to responses
type ResponseTransformer[Entity any, Response any] interface {
	EntityToResponse(entity *Entity) Response
}

// CRUDHandler provides generic CRUD operations for entities
type CRUDHandler[T any, CreateReq any, UpdateReq any, Response any] struct {
	repository          Repository[T]
	requestTransformer  RequestTransformer[CreateReq, UpdateReq, T]
	responseTransformer ResponseTransformer[T, Response]
	errorHandler        *ErrorHandler
	httpUtils           *HTTPUtils
	entityName          string
}

// NewCRUDHandler creates a new generic CRUD handler
func NewCRUDHandler[T any, CreateReq any, UpdateReq any, Response any](
	repo Repository[T],
	requestTransformer RequestTransformer[CreateReq, UpdateReq, T],
	responseTransformer ResponseTransformer[T, Response],
	entityName string,
) *CRUDHandler[T, CreateReq, UpdateReq, Response] {
	return &CRUDHandler[T, CreateReq, UpdateReq, Response]{
		repository:          repo,
		requestTransformer:  requestTransformer,
		responseTransformer: responseTransformer,
		errorHandler:        NewErrorHandler(),
		httpUtils:           NewHTTPUtils(),
		entityName:          entityName,
	}
}

// GetByID handles GET /{entity}/{id} requests
func (h *CRUDHandler[T, CreateReq, UpdateReq, Response]) GetByID(c *gin.Context) {
	// Parse and validate ID parameter
	id, ok := h.httpUtils.ParseUUIDParam(c, "id")
	if !ok {
		return
	}

	// Fetch entity from repository
	entity, err := h.repository.GetByID(c.Request.Context(), id)
	if h.errorHandler.HandleRepositoryError(c, err, h.entityName, "fetch") {
		return
	}

	// Transform to response and return
	response := h.responseTransformer.EntityToResponse(entity)
	h.errorHandler.Success(c, response)
}

// List handles GET /{entity} requests with pagination and filtering
func (h *CRUDHandler[T, CreateReq, UpdateReq, Response]) List(c *gin.Context) {
	// Build filter from query parameters
	filter := h.httpUtils.BuildListFilter(c)

	// Convert to repository filter format
	repoFilter := repositories.ListFilter{
		Page:      filter.Page,
		PageSize:  filter.PageSize,
		Search:    filter.Search,
		SortBy:    filter.SortBy,
		SortOrder: filter.SortDir,
	}

	// Fetch entities from repository
	entities, total, err := h.repository.List(c.Request.Context(), repoFilter)
	if h.errorHandler.HandleRepositoryError(c, err, h.entityName, "list") {
		return
	}

	// Transform entities to responses
	responses := make([]Response, len(entities))
	for i, entity := range entities {
		responses[i] = h.responseTransformer.EntityToResponse(entity)
	}

	// Build paginated response
	listResponse := BuildListResponse(responses, total, filter.Page, filter.PageSize)
	h.errorHandler.Success(c, listResponse)
}

// Create handles POST /{entity} requests
func (h *CRUDHandler[T, CreateReq, UpdateReq, Response]) Create(c *gin.Context) {
	// Bind and validate request
	var req CreateReq
	if !h.httpUtils.BindAndValidate(c, &req) {
		return
	}

	// Transform request to entity
	entity := h.requestTransformer.CreateRequestToEntity(&req)

	// Create entity in repository
	if err := h.repository.Create(c.Request.Context(), entity); err != nil {
		h.errorHandler.HandleRepositoryError(c, err, h.entityName, "create")
		return
	}

	// Transform to response and return
	response := h.responseTransformer.EntityToResponse(entity)
	h.errorHandler.Created(c, response)
}

// Update handles PUT /{entity}/{id} requests
func (h *CRUDHandler[T, CreateReq, UpdateReq, Response]) Update(c *gin.Context) {
	// Parse and validate ID parameter
	id, ok := h.httpUtils.ParseUUIDParam(c, "id")
	if !ok {
		return
	}

	// Bind and validate request
	var req UpdateReq
	if !h.httpUtils.BindAndValidate(c, &req) {
		return
	}

	// Fetch existing entity
	existing, err := h.repository.GetByID(c.Request.Context(), id)
	if h.errorHandler.HandleRepositoryError(c, err, h.entityName, "fetch") {
		return
	}

	// Transform request to updated entity
	updated := h.requestTransformer.UpdateRequestToEntity(&req, existing)

	// Update entity in repository
	if err := h.repository.Update(c.Request.Context(), updated); err != nil {
		h.errorHandler.HandleRepositoryError(c, err, h.entityName, "update")
		return
	}

	// Transform to response and return
	response := h.responseTransformer.EntityToResponse(updated)
	h.errorHandler.Success(c, response)
}

// Delete handles DELETE /{entity}/{id} requests
func (h *CRUDHandler[T, CreateReq, UpdateReq, Response]) Delete(c *gin.Context) {
	// Parse and validate ID parameter
	id, ok := h.httpUtils.ParseUUIDParam(c, "id")
	if !ok {
		return
	}

	// Check if entity exists and get it for protection check
	entity, err := h.repository.GetByID(c.Request.Context(), id)
	if h.errorHandler.HandleRepositoryError(c, err, h.entityName, "fetch") {
		return
	}

	// Check if entity is protected (if it implements a Protected method)
	if protectedEntity, ok := any(entity).(interface{ IsProtected() bool }); ok {
		if protectedEntity.IsProtected() {
			h.errorHandler.HandleProtectedEntityError(c, h.entityName)
			return
		}
	}

	// Soft delete the entity
	if err := h.repository.SoftDelete(c.Request.Context(), id); err != nil {
		h.errorHandler.HandleRepositoryError(c, err, h.entityName, "delete")
		return
	}

	h.errorHandler.NoContent(c)
}

// Exists handles HEAD /{entity}/{id} requests
func (h *CRUDHandler[T, CreateReq, UpdateReq, Response]) Exists(c *gin.Context) {
	// Parse and validate ID parameter
	id, ok := h.httpUtils.ParseUUIDParam(c, "id")
	if !ok {
		return
	}

	// Check if entity exists
	exists, err := h.repository.Exists(c.Request.Context(), id)
	if err != nil {
		h.errorHandler.HandleRepositoryError(c, err, h.entityName, "check existence")
		return
	}

	if exists {
		c.Status(200)
	} else {
		c.Status(404)
	}
}
