package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// PolicyHandler handles HTTP requests for policy operations
type PolicyHandler struct {
	policyRepo  repositories.PolicyRepository
	productRepo repositories.ProductRepository
}

// NewPolicyHandler creates a new policy handler
func NewPolicyHandler(policyRepo repositories.PolicyRepository, productRepo repositories.ProductRepository) *PolicyHandler {
	return &PolicyHandler{
		policyRepo:  policyRepo,
		productRepo: productRepo,
	}
}

// Request types - simple, flat Go structs
type CreatePolicyRequest struct {
	Name      string `json:"name" binding:"required"`
	ProductID string `json:"productId" binding:"required"`

	// Core settings
	Scheme    string `json:"scheme,omitempty"`
	Duration  *int64 `json:"duration,omitempty"`
	Encrypted bool   `json:"encrypted"`
	UsePool   bool   `json:"usePool"`
	Strict    bool   `json:"strict"`
	Floating  bool   `json:"floating"`
	Protected bool   `json:"protected"`

	// Limits
	MaxMachines  *int `json:"maxMachines,omitempty"`
	MaxProcesses *int `json:"maxProcesses,omitempty"`
	MaxUsers     *int `json:"maxUsers,omitempty"`
	MaxCores     *int `json:"maxCores,omitempty"`
	MaxUses      *int `json:"maxUses,omitempty"`

	// Machine strategies
	MachineUniquenessStrategy string `json:"machineUniquenessStrategy,omitempty"`
	MachineMatchingStrategy   string `json:"machineMatchingStrategy,omitempty"`

	// Heartbeat settings
	RequireHeartbeat              bool   `json:"requireHeartbeat"`
	HeartbeatDuration             *int   `json:"heartbeatDuration,omitempty"`
	HeartbeatCullStrategy         string `json:"heartbeatCullStrategy,omitempty"`
	HeartbeatResurrectionStrategy string `json:"heartbeatResurrectionStrategy,omitempty"`
	HeartbeatBasis                string `json:"heartbeatBasis,omitempty"`

	// Metadata
	Metadata map[string]any `json:"metadata,omitempty"`
}

type UpdatePolicyRequest struct {
	Name      string `json:"name,omitempty"`
	ProductID string `json:"productId,omitempty"`

	// Core settings
	Scheme    string `json:"scheme,omitempty"`
	Duration  *int64 `json:"duration,omitempty"`
	Encrypted *bool  `json:"encrypted,omitempty"`
	UsePool   *bool  `json:"usePool,omitempty"`
	Strict    *bool  `json:"strict,omitempty"`
	Floating  *bool  `json:"floating,omitempty"`
	Protected *bool  `json:"protected,omitempty"`

	// Limits
	MaxMachines  *int `json:"maxMachines,omitempty"`
	MaxProcesses *int `json:"maxProcesses,omitempty"`
	MaxUsers     *int `json:"maxUsers,omitempty"`
	MaxCores     *int `json:"maxCores,omitempty"`
	MaxUses      *int `json:"maxUses,omitempty"`

	// Machine strategies
	MachineUniquenessStrategy string `json:"machineUniquenessStrategy,omitempty"`
	MachineMatchingStrategy   string `json:"machineMatchingStrategy,omitempty"`

	// Heartbeat settings
	RequireHeartbeat              *bool  `json:"requireHeartbeat,omitempty"`
	HeartbeatDuration             *int   `json:"heartbeatDuration,omitempty"`
	HeartbeatCullStrategy         string `json:"heartbeatCullStrategy,omitempty"`
	HeartbeatResurrectionStrategy string `json:"heartbeatResurrectionStrategy,omitempty"`
	HeartbeatBasis                string `json:"heartbeatBasis,omitempty"`

	// Metadata
	Metadata map[string]any `json:"metadata,omitempty"`
}

// Response types - simple, flat Go structs
type PolicyResponse struct {
	ID   string `json:"id"`
	Name string `json:"name"`

	// Core settings
	Scheme    *string `json:"scheme,omitempty"`
	Duration  *int64  `json:"duration,omitempty"`
	Encrypted bool    `json:"encrypted"`
	UsePool   bool    `json:"usePool"`
	Strict    bool    `json:"strict"`
	Floating  bool    `json:"floating"`
	Protected bool    `json:"protected"`

	// Limits
	MaxMachines  *int `json:"maxMachines,omitempty"`
	MaxProcesses *int `json:"maxProcesses,omitempty"`
	MaxUsers     *int `json:"maxUsers,omitempty"`
	MaxCores     *int `json:"maxCores,omitempty"`
	MaxUses      *int `json:"maxUses,omitempty"`

	// Machine strategies
	MachineUniquenessStrategy *string `json:"machineUniquenessStrategy,omitempty"`
	MachineMatchingStrategy   *string `json:"machineMatchingStrategy,omitempty"`

	// Heartbeat settings
	RequireHeartbeat              bool    `json:"requireHeartbeat"`
	HeartbeatDuration             *int    `json:"heartbeatDuration,omitempty"`
	HeartbeatCullStrategy         *string `json:"heartbeatCullStrategy,omitempty"`
	HeartbeatResurrectionStrategy *string `json:"heartbeatResurrectionStrategy,omitempty"`
	HeartbeatBasis                *string `json:"heartbeatBasis,omitempty"`

	// Metadata and timestamps
	Metadata  map[string]any `json:"metadata,omitempty"`
	ProductID string         `json:"productId"`
	CreatedAt time.Time      `json:"createdAt"`
	UpdatedAt time.Time      `json:"updatedAt"`
}

type PolicyListResponse struct {
	Policies []PolicyResponse `json:"policies"`
	Total    int64            `json:"total"`
	Page     int              `json:"page"`
	Limit    int              `json:"limit"`
}

// GetPolicies handles GET /policies - list policies
func (h *PolicyHandler) GetPolicies(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// Build filter
	filter := repositories.ListFilter{
		Page:     page,
		PageSize: limit,
	}

	// Get policies from repository
	policies, total, err := h.policyRepo.List(c.Request.Context(), filter)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch policies")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch policies",
			},
		})
		return
	}

	// Convert to response format
	policyResponses := make([]PolicyResponse, len(policies))
	for i, policy := range policies {
		policyResponses[i] = h.entityToResponse(policy)
	}

	c.JSON(http.StatusOK, PolicyListResponse{
		Policies: policyResponses,
		Total:    total,
		Page:     page,
		Limit:    limit,
	})
}

// GetPolicy handles GET /policies/:id - get single policy
func (h *PolicyHandler) GetPolicy(c *gin.Context) {
	policyID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_POLICY_ID",
				Message: "Invalid policy ID format",
			},
		})
		return
	}

	policy, err := h.policyRepo.GetByID(c.Request.Context(), policyID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "POLICY_NOT_FOUND",
					Message: "Policy not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("policy_id", policyID.String()).Msg("Failed to fetch policy")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch policy",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(policy))
}

// CreatePolicy handles POST /policies - create new policy
func (h *PolicyHandler) CreatePolicy(c *gin.Context) {
	var req CreatePolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: err.Error(),
			},
		})
		return
	}

	// Validate product exists
	productID, err := uuid.Parse(req.ProductID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_PRODUCT_ID",
				Message: "Invalid product ID format",
			},
		})
		return
	}

	_, err = h.productRepo.GetByID(c.Request.Context(), productID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
				Error: ErrorDetail{
					Code:    "PRODUCT_NOT_FOUND",
					Message: "Specified product does not exist",
				},
			})
			return
		}
		log.Error().Err(err).Str("product_id", productID.String()).Msg("Failed to validate product")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to validate product",
			},
		})
		return
	}

	// Convert request to entity
	policy := h.requestToEntity(&req)
	policy.ID = uuid.New()
	policy.ProductID = productID

	// Create policy
	if err := h.policyRepo.Create(c.Request.Context(), policy); err != nil {
		log.Error().Err(err).Msg("Failed to create policy")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to create policy",
			},
		})
		return
	}

	c.JSON(http.StatusCreated, h.entityToResponse(policy))
}

// UpdatePolicy handles PUT /policies/:id - update existing policy
func (h *PolicyHandler) UpdatePolicy(c *gin.Context) {
	policyID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_POLICY_ID",
				Message: "Invalid policy ID format",
			},
		})
		return
	}

	var req UpdatePolicyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: err.Error(),
			},
		})
		return
	}

	// Get existing policy
	policy, err := h.policyRepo.GetByID(c.Request.Context(), policyID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "POLICY_NOT_FOUND",
					Message: "Policy not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("policy_id", policyID.String()).Msg("Failed to fetch policy")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch policy",
			},
		})
		return
	}

	// Validate product if provided
	if req.ProductID != "" {
		productID, err := uuid.Parse(req.ProductID)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INVALID_PRODUCT_ID",
					Message: "Invalid product ID format",
				},
			})
			return
		}

		_, err = h.productRepo.GetByID(c.Request.Context(), productID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
					Error: ErrorDetail{
						Code:    "PRODUCT_NOT_FOUND",
						Message: "Specified product does not exist",
					},
				})
				return
			}
			log.Error().Err(err).Str("product_id", productID.String()).Msg("Failed to validate product")
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INTERNAL_ERROR",
					Message: "Failed to validate product",
				},
			})
			return
		}
		policy.ProductID = productID
	}

	// Update policy with request data
	h.updateEntityFromRequest(policy, &req)
	policy.UpdatedAt = time.Now()

	// Save updated policy
	if err := h.policyRepo.Update(c.Request.Context(), policy); err != nil {
		log.Error().Err(err).Str("policy_id", policyID.String()).Msg("Failed to update policy")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to update policy",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(policy))
}

// DeletePolicy handles DELETE /policies/:id - delete policy
func (h *PolicyHandler) DeletePolicy(c *gin.Context) {
	policyID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_POLICY_ID",
				Message: "Invalid policy ID format",
			},
		})
		return
	}

	// Check if policy exists
	_, err = h.policyRepo.GetByID(c.Request.Context(), policyID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "POLICY_NOT_FOUND",
					Message: "Policy not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("policy_id", policyID.String()).Msg("Failed to fetch policy")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch policy",
			},
		})
		return
	}

	// Soft delete the policy
	if err := h.policyRepo.SoftDelete(c.Request.Context(), policyID); err != nil {
		log.Error().Err(err).Str("policy_id", policyID.String()).Msg("Failed to delete policy")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to delete policy",
			},
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// Helper methods for converting between entities and requests/responses

// requestToEntity converts CreatePolicyRequest to Policy entity
func (h *PolicyHandler) requestToEntity(req *CreatePolicyRequest) *entities.Policy {
	policy := &entities.Policy{
		Name:      req.Name,
		Duration:  req.Duration,
		Encrypted: req.Encrypted,
		UsePool:   req.UsePool,
		Strict:    req.Strict,
		Floating:  req.Floating,
		Protected: req.Protected,

		// Limits
		MaxMachines:  req.MaxMachines,
		MaxProcesses: req.MaxProcesses,
		MaxUsers:     req.MaxUsers,
		MaxCores:     req.MaxCores,
		MaxUses:      req.MaxUses,

		// Heartbeat settings
		RequireHeartbeat:  req.RequireHeartbeat,
		HeartbeatDuration: req.HeartbeatDuration,

		// Metadata
		Metadata: req.Metadata,

		// Timestamps
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	// Set crypto scheme if provided
	if req.Scheme != "" {
		scheme := entities.CryptoScheme(req.Scheme)
		policy.Scheme = &scheme
	}

	// Set machine strategies if provided
	if req.MachineUniquenessStrategy != "" {
		strategy := entities.MachineUniquenessStrategy(req.MachineUniquenessStrategy)
		policy.MachineUniquenessStrategy = &strategy
	}
	if req.MachineMatchingStrategy != "" {
		strategy := entities.MatchingStrategy(req.MachineMatchingStrategy)
		policy.MachineMatchingStrategy = &strategy
	}

	// Set heartbeat strategies if provided
	if req.HeartbeatCullStrategy != "" {
		strategy := entities.HeartbeatCullStrategy(req.HeartbeatCullStrategy)
		policy.HeartbeatCullStrategy = &strategy
	}
	if req.HeartbeatResurrectionStrategy != "" {
		strategy := entities.HeartbeatResurrectionStrategy(req.HeartbeatResurrectionStrategy)
		policy.HeartbeatResurrectionStrategy = &strategy
	}
	if req.HeartbeatBasis != "" {
		basis := entities.HeartbeatBasis(req.HeartbeatBasis)
		policy.HeartbeatBasis = &basis
	}

	// Set default strategies
	policy.SetDefaultStrategies()

	return policy
}

// updateEntityFromRequest updates an existing policy with request data
func (h *PolicyHandler) updateEntityFromRequest(policy *entities.Policy, req *UpdatePolicyRequest) {
	// Update basic fields if provided
	if req.Name != "" {
		policy.Name = req.Name
	}

	// Update optional fields only if provided
	if req.Duration != nil {
		policy.Duration = req.Duration
	}
	if req.Encrypted != nil {
		policy.Encrypted = *req.Encrypted
	}
	if req.UsePool != nil {
		policy.UsePool = *req.UsePool
	}
	if req.Strict != nil {
		policy.Strict = *req.Strict
	}
	if req.Floating != nil {
		policy.Floating = *req.Floating
	}
	if req.Protected != nil {
		policy.Protected = *req.Protected
	}

	// Update limits if provided
	if req.MaxMachines != nil {
		policy.MaxMachines = req.MaxMachines
	}
	if req.MaxProcesses != nil {
		policy.MaxProcesses = req.MaxProcesses
	}
	if req.MaxUsers != nil {
		policy.MaxUsers = req.MaxUsers
	}
	if req.MaxCores != nil {
		policy.MaxCores = req.MaxCores
	}
	if req.MaxUses != nil {
		policy.MaxUses = req.MaxUses
	}

	// Update strategies if provided
	if req.MachineUniquenessStrategy != "" {
		strategy := entities.MachineUniquenessStrategy(req.MachineUniquenessStrategy)
		policy.MachineUniquenessStrategy = &strategy
	}
	if req.MachineMatchingStrategy != "" {
		strategy := entities.MatchingStrategy(req.MachineMatchingStrategy)
		policy.MachineMatchingStrategy = &strategy
	}

	// Update heartbeat settings if provided
	if req.RequireHeartbeat != nil {
		policy.RequireHeartbeat = *req.RequireHeartbeat
	}
	if req.HeartbeatDuration != nil {
		policy.HeartbeatDuration = req.HeartbeatDuration
	}
	if req.HeartbeatCullStrategy != "" {
		strategy := entities.HeartbeatCullStrategy(req.HeartbeatCullStrategy)
		policy.HeartbeatCullStrategy = &strategy
	}
	if req.HeartbeatResurrectionStrategy != "" {
		strategy := entities.HeartbeatResurrectionStrategy(req.HeartbeatResurrectionStrategy)
		policy.HeartbeatResurrectionStrategy = &strategy
	}
	if req.HeartbeatBasis != "" {
		basis := entities.HeartbeatBasis(req.HeartbeatBasis)
		policy.HeartbeatBasis = &basis
	}

	// Update crypto scheme if provided
	if req.Scheme != "" {
		scheme := entities.CryptoScheme(req.Scheme)
		policy.Scheme = &scheme
	}

	// Update metadata if provided
	if req.Metadata != nil {
		policy.Metadata = req.Metadata
	}
}

// entityToResponse converts Policy entity to PolicyResponse
func (h *PolicyHandler) entityToResponse(policy *entities.Policy) PolicyResponse {
	response := PolicyResponse{
		ID:        policy.ID.String(),
		Name:      policy.Name,
		Duration:  policy.Duration,
		Encrypted: policy.Encrypted,
		UsePool:   policy.UsePool,
		Strict:    policy.Strict,
		Floating:  policy.Floating,
		Protected: policy.Protected,

		// Limits
		MaxMachines:  policy.MaxMachines,
		MaxProcesses: policy.MaxProcesses,
		MaxUsers:     policy.MaxUsers,
		MaxCores:     policy.MaxCores,
		MaxUses:      policy.MaxUses,

		// Heartbeat settings
		RequireHeartbeat:  policy.RequireHeartbeat,
		HeartbeatDuration: policy.HeartbeatDuration,

		// Metadata and timestamps
		Metadata:  policy.Metadata,
		ProductID: policy.ProductID.String(),
		CreatedAt: policy.CreatedAt,
		UpdatedAt: policy.UpdatedAt,
	}

	// Convert enum pointers to strings
	if policy.Scheme != nil {
		scheme := string(*policy.Scheme)
		response.Scheme = &scheme
	}
	if policy.MachineUniquenessStrategy != nil {
		strategy := string(*policy.MachineUniquenessStrategy)
		response.MachineUniquenessStrategy = &strategy
	}
	if policy.MachineMatchingStrategy != nil {
		strategy := string(*policy.MachineMatchingStrategy)
		response.MachineMatchingStrategy = &strategy
	}
	if policy.HeartbeatCullStrategy != nil {
		strategy := string(*policy.HeartbeatCullStrategy)
		response.HeartbeatCullStrategy = &strategy
	}
	if policy.HeartbeatResurrectionStrategy != nil {
		strategy := string(*policy.HeartbeatResurrectionStrategy)
		response.HeartbeatResurrectionStrategy = &strategy
	}
	if policy.HeartbeatBasis != nil {
		basis := string(*policy.HeartbeatBasis)
		response.HeartbeatBasis = &basis
	}

	return response
}
