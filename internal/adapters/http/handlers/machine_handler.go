package handlers

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/google/uuid"
	"github.com/rs/zerolog/log"
	"gorm.io/gorm"
)

// MachineHandler handles HTTP requests for machine operations
type MachineHandler struct {
	machineRepo repositories.MachineRepository
	licenseRepo repositories.LicenseRepository
	userRepo    repositories.UserRepository
}

// NewMachineHandler creates a new machine handler
func NewMachineHandler(machineRepo repositories.MachineRepository, licenseRepo repositories.LicenseRepository, userRepo repositories.UserRepository) *MachineHandler {
	return &MachineHandler{
		machineRepo: machineRepo,
		licenseRepo: licenseRepo,
		userRepo:    userRepo,
	}
}

// Request types - simple, flat Go structs
type CreateMachineRequest struct {
	LicenseID   string `json:"licenseId" binding:"required"`
	Fingerprint string `json:"fingerprint" binding:"required"`

	// Optional identification fields
	Name     string `json:"name,omitempty"`
	Hostname string `json:"hostname,omitempty"`
	Platform string `json:"platform,omitempty"`
	IP       string `json:"ip,omitempty"`

	// Hardware information
	Cores *int `json:"cores,omitempty"`

	// Process override
	MaxProcessesOverride *int `json:"maxProcessesOverride,omitempty"`

	// Owner assignment
	OwnerID string `json:"ownerId,omitempty"`

	// Components
	Components []MachineComponentRequest `json:"components,omitempty"`

	// Metadata
	Metadata map[string]any `json:"metadata,omitempty"`
}

type UpdateMachineRequest struct {
	// Optional identification fields
	Name     string `json:"name,omitempty"`
	Hostname string `json:"hostname,omitempty"`
	Platform string `json:"platform,omitempty"`
	IP       string `json:"ip,omitempty"`

	// Hardware information
	Cores *int `json:"cores,omitempty"`

	// Process override
	MaxProcessesOverride *int `json:"maxProcessesOverride,omitempty"`

	// Owner assignment
	OwnerID string `json:"ownerId,omitempty"`

	// Metadata
	Metadata map[string]any `json:"metadata,omitempty"`
}

type MachineComponentRequest struct {
	Name        string `json:"name" binding:"required"`
	Fingerprint string `json:"fingerprint" binding:"required"`
}

// Response types - simple, flat Go structs
type MachineResponse struct {
	ID          string `json:"id"`
	LicenseID   string `json:"licenseId"`
	PolicyID    string `json:"policyId"`
	Fingerprint string `json:"fingerprint"`

	// Optional identification fields
	Name     *string `json:"name,omitempty"`
	Hostname *string `json:"hostname,omitempty"`
	Platform *string `json:"platform,omitempty"`
	IP       *string `json:"ip,omitempty"`

	// Hardware information
	Cores *int `json:"cores,omitempty"`

	// Status and state
	Status        string     `json:"status"`
	ActivatedAt   *time.Time `json:"activatedAt,omitempty"`
	DeactivatedAt *time.Time `json:"deactivatedAt,omitempty"`
	LastSeen      *time.Time `json:"lastSeen,omitempty"`

	// Heartbeat information
	RequireHeartbeat     bool       `json:"requireHeartbeat"`
	HeartbeatStatus      string     `json:"heartbeatStatus"`
	HeartbeatDuration    *int       `json:"heartbeatDuration,omitempty"`
	LastHeartbeat        *time.Time `json:"lastHeartbeat,omitempty"`
	NextHeartbeat        *time.Time `json:"nextHeartbeat,omitempty"`
	LastDeathEventSentAt *time.Time `json:"lastDeathEventSentAt,omitempty"`

	// Check-out tracking
	LastCheckOut *time.Time `json:"lastCheckOut,omitempty"`

	// Process information
	MaxProcesses         *int `json:"maxProcesses,omitempty"`
	MaxProcessesOverride *int `json:"maxProcessesOverride,omitempty"`

	// Owner information
	OwnerID *string `json:"ownerId,omitempty"`

	// Components
	Components []MachineComponentResponse `json:"components,omitempty"`

	// Metadata and timestamps
	Metadata  map[string]any `json:"metadata,omitempty"`
	CreatedAt time.Time      `json:"createdAt"`
	UpdatedAt time.Time      `json:"updatedAt"`
}

type MachineComponentResponse struct {
	Name        string `json:"name"`
	Fingerprint string `json:"fingerprint"`
}

type MachineListResponse struct {
	Machines []MachineResponse `json:"machines"`
	Total    int64             `json:"total"`
	Page     int               `json:"page"`
	Limit    int               `json:"limit"`
}

// Heartbeat action requests
type HeartbeatPingRequest struct {
	// Empty - ping just updates heartbeat timestamp
}

type HeartbeatResetRequest struct {
	// Empty - reset clears heartbeat data
}

// GetMachines handles GET /machines - list machines
func (h *MachineHandler) GetMachines(c *gin.Context) {
	// Parse query parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	// Build filter
	filter := repositories.ListFilter{
		Page:     page,
		PageSize: limit,
	}

	// Get machines from repository
	machines, total, err := h.machineRepo.List(c.Request.Context(), filter)
	if err != nil {
		log.Error().Err(err).Msg("Failed to fetch machines")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch machines",
			},
		})
		return
	}

	// Convert to response format
	machineResponses := make([]MachineResponse, len(machines))
	for i, machine := range machines {
		machineResponses[i] = h.entityToResponse(machine)
	}

	c.JSON(http.StatusOK, MachineListResponse{
		Machines: machineResponses,
		Total:    total,
		Page:     page,
		Limit:    limit,
	})
}

// GetMachine handles GET /machines/:id - get single machine
func (h *MachineHandler) GetMachine(c *gin.Context) {
	machineID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_MACHINE_ID",
				Message: "Invalid machine ID format",
			},
		})
		return
	}

	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "MACHINE_NOT_FOUND",
					Message: "Machine not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to fetch machine")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch machine",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(machine))
}

// CreateMachine handles POST /machines - create new machine
func (h *MachineHandler) CreateMachine(c *gin.Context) {
	var req CreateMachineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: err.Error(),
			},
		})
		return
	}

	// Validate license exists
	licenseID, err := uuid.Parse(req.LicenseID)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_LICENSE_ID",
				Message: "Invalid license ID format",
			},
		})
		return
	}

	license, err := h.licenseRepo.GetByID(c.Request.Context(), licenseID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
				Error: ErrorDetail{
					Code:    "LICENSE_NOT_FOUND",
					Message: "Specified license does not exist",
				},
			})
			return
		}
		log.Error().Err(err).Str("license_id", licenseID.String()).Msg("Failed to validate license")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to validate license",
			},
		})
		return
	}

	// Convert request to entity
	machine := h.requestToEntity(&req)
	machine.ID = uuid.New()
	machine.LicenseID = licenseID
	machine.PolicyID = license.PolicyID

	// Validate owner if provided
	if req.OwnerID != "" {
		ownerID, err := uuid.Parse(req.OwnerID)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INVALID_OWNER_ID",
					Message: "Invalid owner ID format",
				},
			})
			return
		}

		_, err = h.userRepo.GetByID(c.Request.Context(), ownerID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
					Error: ErrorDetail{
						Code:    "OWNER_NOT_FOUND",
						Message: "Specified owner does not exist",
					},
				})
				return
			}
			log.Error().Err(err).Str("owner_id", ownerID.String()).Msg("Failed to validate owner")
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INTERNAL_ERROR",
					Message: "Failed to validate owner",
				},
			})
			return
		}
		machine.OwnerID = &ownerID
	}

	// Create machine
	if err := h.machineRepo.Create(c.Request.Context(), machine); err != nil {
		log.Error().Err(err).Msg("Failed to create machine")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to create machine",
			},
		})
		return
	}

	c.JSON(http.StatusCreated, h.entityToResponse(machine))
}

// UpdateMachine handles PUT /machines/:id - update existing machine
func (h *MachineHandler) UpdateMachine(c *gin.Context) {
	machineID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_MACHINE_ID",
				Message: "Invalid machine ID format",
			},
		})
		return
	}

	var req UpdateMachineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_REQUEST",
				Message: err.Error(),
			},
		})
		return
	}

	// Get existing machine
	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "MACHINE_NOT_FOUND",
					Message: "Machine not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to fetch machine")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch machine",
			},
		})
		return
	}

	// Validate owner if provided
	if req.OwnerID != "" {
		ownerID, err := uuid.Parse(req.OwnerID)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INVALID_OWNER_ID",
					Message: "Invalid owner ID format",
				},
			})
			return
		}

		_, err = h.userRepo.GetByID(c.Request.Context(), ownerID)
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
					Error: ErrorDetail{
						Code:    "OWNER_NOT_FOUND",
						Message: "Specified owner does not exist",
					},
				})
				return
			}
			log.Error().Err(err).Str("owner_id", ownerID.String()).Msg("Failed to validate owner")
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INTERNAL_ERROR",
					Message: "Failed to validate owner",
				},
			})
			return
		}
		machine.OwnerID = &ownerID
	}

	// Update machine with request data
	h.updateEntityFromRequest(machine, &req)
	machine.UpdatedAt = time.Now()

	// Save updated machine
	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to update machine")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to update machine",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(machine))
}

// DeleteMachine handles DELETE /machines/:id - delete machine
func (h *MachineHandler) DeleteMachine(c *gin.Context) {
	machineID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_MACHINE_ID",
				Message: "Invalid machine ID format",
			},
		})
		return
	}

	// Check if machine exists
	_, err = h.machineRepo.GetByID(c.Request.Context(), machineID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "MACHINE_NOT_FOUND",
					Message: "Machine not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to fetch machine")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch machine",
			},
		})
		return
	}

	// Soft delete the machine
	if err := h.machineRepo.SoftDelete(c.Request.Context(), machineID); err != nil {
		log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to delete machine")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to delete machine",
			},
		})
		return
	}

	c.Status(http.StatusNoContent)
}

// HeartbeatPing handles POST /machines/:id/actions/heartbeat/ping - ping heartbeat
func (h *MachineHandler) HeartbeatPing(c *gin.Context) {
	machineID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_MACHINE_ID",
				Message: "Invalid machine ID format",
			},
		})
		return
	}

	// Get machine
	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "MACHINE_NOT_FOUND",
					Message: "Machine not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to fetch machine")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch machine",
			},
		})
		return
	}

	// Handle resurrection if machine is dead
	if machine.IsDead() {
		if err := machine.Resurrect(); err != nil {
			if err == entities.ErrResurrectionUnsupported {
				c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
					Error: ErrorDetail{
						Code:    "MACHINE_HEARTBEAT_RESURRECTION_UNSUPPORTED",
						Message: "Machine resurrection is not supported",
					},
				})
				return
			}
			if err == entities.ErrResurrectionExpired {
				c.JSON(http.StatusUnprocessableEntity, ErrorResponse{
					Error: ErrorDetail{
						Code:    "MACHINE_HEARTBEAT_RESURRECTION_EXPIRED",
						Message: "Machine resurrection period has expired",
					},
				})
				return
			}
			log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to resurrect machine")
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INTERNAL_ERROR",
					Message: "Failed to resurrect machine",
				},
			})
			return
		}
	} else {
		// Normal ping
		if err := machine.Ping(); err != nil {
			log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to ping machine")
			c.JSON(http.StatusInternalServerError, ErrorResponse{
				Error: ErrorDetail{
					Code:    "INTERNAL_ERROR",
					Message: "Failed to ping machine",
				},
			})
			return
		}
	}

	// Save updated machine
	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to update machine heartbeat")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to update machine heartbeat",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(machine))
}

// HeartbeatReset handles POST /machines/:id/actions/heartbeat/reset - reset heartbeat
func (h *MachineHandler) HeartbeatReset(c *gin.Context) {
	machineID, err := uuid.Parse(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INVALID_MACHINE_ID",
				Message: "Invalid machine ID format",
			},
		})
		return
	}

	// Get machine
	machine, err := h.machineRepo.GetByID(c.Request.Context(), machineID)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, ErrorResponse{
				Error: ErrorDetail{
					Code:    "MACHINE_NOT_FOUND",
					Message: "Machine not found",
				},
			})
			return
		}
		log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to fetch machine")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to fetch machine",
			},
		})
		return
	}

	// Reset heartbeat
	if err := machine.Reset(); err != nil {
		log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to reset machine heartbeat")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to reset machine heartbeat",
			},
		})
		return
	}

	// Save updated machine
	if err := h.machineRepo.Update(c.Request.Context(), machine); err != nil {
		log.Error().Err(err).Str("machine_id", machineID.String()).Msg("Failed to update machine heartbeat")
		c.JSON(http.StatusInternalServerError, ErrorResponse{
			Error: ErrorDetail{
				Code:    "INTERNAL_ERROR",
				Message: "Failed to update machine heartbeat",
			},
		})
		return
	}

	c.JSON(http.StatusOK, h.entityToResponse(machine))
}

// Helper methods for converting between entities and requests/responses

// entityToResponse converts Machine entity to MachineResponse
func (h *MachineHandler) entityToResponse(machine *entities.Machine) MachineResponse {
	response := MachineResponse{
		ID:            machine.ID.String(),
		LicenseID:     machine.LicenseID.String(),
		PolicyID:      machine.PolicyID.String(),
		Fingerprint:   machine.Fingerprint,
		Name:          machine.Name,
		Hostname:      machine.Hostname,
		Platform:      machine.Platform,
		IP:            machine.IP,
		Cores:         machine.Cores,
		Status:        string(machine.Status),
		ActivatedAt:   machine.ActivatedAt,
		DeactivatedAt: machine.DeactivatedAt,
		LastSeen:      machine.LastSeen,

		// Heartbeat information
		RequireHeartbeat:     machine.RequiresHeartbeat(),
		HeartbeatStatus:      string(machine.GetHeartbeatStatus()),
		LastHeartbeat:        machine.LastHeartbeatAt,
		NextHeartbeat:        machine.GetNextHeartbeatAt(),
		LastDeathEventSentAt: machine.LastDeathEventSentAt,

		// Check-out tracking
		LastCheckOut: machine.LastCheckOutAt,

		// Process information
		MaxProcessesOverride: machine.MaxProcessesOverride,

		// Metadata and timestamps
		Metadata:  machine.Metadata,
		CreatedAt: machine.CreatedAt,
		UpdatedAt: machine.UpdatedAt,
	}

	// Set heartbeat duration if available
	if machine.RequiresHeartbeat() {
		duration := int(machine.HeartbeatDuration().Seconds())
		response.HeartbeatDuration = &duration
	}

	// Set max processes from machine or license
	if maxProc := machine.GetMaxProcesses(); maxProc != nil {
		response.MaxProcesses = maxProc
	}

	// Set owner ID if present
	if machine.OwnerID != nil {
		ownerID := machine.OwnerID.String()
		response.OwnerID = &ownerID
	}

	// Convert components
	if len(machine.Components) > 0 {
		response.Components = make([]MachineComponentResponse, len(machine.Components))
		for i, component := range machine.Components {
			response.Components[i] = MachineComponentResponse{
				Name:        component.Name,
				Fingerprint: component.Fingerprint,
			}
		}
	}

	return response
}

// requestToEntity converts CreateMachineRequest to Machine entity
func (h *MachineHandler) requestToEntity(req *CreateMachineRequest) *entities.Machine {
	machine := &entities.Machine{
		Fingerprint:          req.Fingerprint,
		Cores:                req.Cores,
		MaxProcessesOverride: req.MaxProcessesOverride,
		Status:               entities.MachineStatusActive,
		Metadata:             req.Metadata,
		CreatedAt:            time.Now(),
		UpdatedAt:            time.Now(),
	}

	// Set optional string fields
	if req.Name != "" {
		machine.Name = &req.Name
	}
	if req.Hostname != "" {
		machine.Hostname = &req.Hostname
	}
	if req.Platform != "" {
		machine.Platform = &req.Platform
	}
	if req.IP != "" {
		machine.IP = &req.IP
	}

	// Convert components
	if len(req.Components) > 0 {
		machine.Components = make([]entities.MachineComponent, len(req.Components))
		for i, component := range req.Components {
			machine.Components[i] = entities.MachineComponent{
				Name:        component.Name,
				Fingerprint: component.Fingerprint,
			}
		}
	}

	return machine
}

// updateEntityFromRequest updates Machine entity with UpdateMachineRequest data
func (h *MachineHandler) updateEntityFromRequest(machine *entities.Machine, req *UpdateMachineRequest) {
	// Update optional string fields
	if req.Name != "" {
		machine.Name = &req.Name
	}
	if req.Hostname != "" {
		machine.Hostname = &req.Hostname
	}
	if req.Platform != "" {
		machine.Platform = &req.Platform
	}
	if req.IP != "" {
		machine.IP = &req.IP
	}

	// Update numeric fields
	if req.Cores != nil {
		machine.Cores = req.Cores
	}
	if req.MaxProcessesOverride != nil {
		machine.MaxProcessesOverride = req.MaxProcessesOverride
	}

	// Update metadata
	if req.Metadata != nil {
		machine.Metadata = req.Metadata
	}
}
