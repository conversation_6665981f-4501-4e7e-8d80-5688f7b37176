package middleware

import (
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/gokeys/gokeys/internal/domain/entities"
	"github.com/gokeys/gokeys/internal/domain/repositories"
	"github.com/gokeys/gokeys/internal/domain/services/auth"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Mock repositories for testing
type MockUserRepository struct {
	mock.Mock
}

func (m *MockUserRepository) GetByID(ctx interface{}, id uuid.UUID) (*entities.User, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*entities.User), args.Error(1)
}

func (m *MockUserRepository) GetByEmail(ctx interface{}, email string) (*entities.User, error) {
	args := m.Called(ctx, email)
	return args.Get(0).(*entities.User), args.Error(1)
}

func (m *MockUserRepository) List(ctx interface{}, filter repositories.ListFilter) ([]*entities.User, int64, error) {
	args := m.Called(ctx, filter)
	return args.Get(0).([]*entities.User), args.Get(1).(int64), args.Error(2)
}

func (m *MockUserRepository) Create(ctx interface{}, user *entities.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) Update(ctx interface{}, user *entities.User) error {
	args := m.Called(ctx, user)
	return args.Error(0)
}

func (m *MockUserRepository) Delete(ctx interface{}, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockUserRepository) SoftDelete(ctx interface{}, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

type MockSessionRepository struct {
	mock.Mock
}

func (m *MockSessionRepository) GetByID(ctx interface{}, id uuid.UUID) (*entities.Session, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*entities.Session), args.Error(1)
}

func (m *MockSessionRepository) Create(ctx interface{}, session *entities.Session) error {
	args := m.Called(ctx, session)
	return args.Error(0)
}

func (m *MockSessionRepository) Update(ctx interface{}, session *entities.Session) error {
	args := m.Called(ctx, session)
	return args.Error(0)
}

func (m *MockSessionRepository) Delete(ctx interface{}, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

type MockAPITokenRepository struct {
	mock.Mock
}

func (m *MockAPITokenRepository) GetByID(ctx interface{}, id uuid.UUID) (*entities.APIToken, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(*entities.APIToken), args.Error(1)
}

func (m *MockAPITokenRepository) Create(ctx interface{}, token *entities.APIToken) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *MockAPITokenRepository) Update(ctx interface{}, token *entities.APIToken) error {
	args := m.Called(ctx, token)
	return args.Error(0)
}

func (m *MockAPITokenRepository) Delete(ctx interface{}, id uuid.UUID) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

type MockAuthService struct {
	mock.Mock
}

func (m *MockAuthService) VerifyJWT(token string) (map[string]interface{}, error) {
	args := m.Called(token)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func (m *MockAuthService) ValidateAPIToken(token string) (map[string]interface{}, error) {
	args := m.Called(token)
	return args.Get(0).(map[string]interface{}), args.Error(1)
}

func TestAuthenticationMiddleware_RequireAuthentication_NoCredentials(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup mocks
	mockUserRepo := &MockUserRepository{}
	mockSessionRepo := &MockSessionRepository{}
	mockAPITokenRepo := &MockAPITokenRepository{}
	mockAuthService := &MockAuthService{}

	// Create middleware
	authMW := NewAuthenticationMiddleware(
		mockUserRepo,
		mockSessionRepo,
		mockAPITokenRepo,
		(*auth.AuthService)(nil), // Cast to satisfy type
	)

	// Setup test router
	router := gin.New()
	router.Use(authMW.RequireAuthentication())
	router.GET("/test", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	// Test request without credentials
	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should return 401 Unauthorized
	assert.Equal(t, http.StatusUnauthorized, w.Code)
	assert.Contains(t, w.Body.String(), "AUTHENTICATION_REQUIRED")
}

func TestAuthenticationMiddleware_OptionalAuthentication_NoCredentials(t *testing.T) {
	gin.SetMode(gin.TestMode)

	// Setup mocks
	mockUserRepo := &MockUserRepository{}
	mockSessionRepo := &MockSessionRepository{}
	mockAPITokenRepo := &MockAPITokenRepository{}
	mockAuthService := &MockAuthService{}

	// Create middleware
	authMW := NewAuthenticationMiddleware(
		mockUserRepo,
		mockSessionRepo,
		mockAPITokenRepo,
		(*auth.AuthService)(nil), // Cast to satisfy type
	)

	// Setup test router
	router := gin.New()
	router.Use(authMW.OptionalAuthentication())
	router.GET("/test", func(c *gin.Context) {
		// Check if user is authenticated
		user, exists := GetCurrentUser(c)
		if exists && user != nil {
			c.JSON(http.StatusOK, gin.H{"authenticated": true, "user_id": user.ID})
		} else {
			c.JSON(http.StatusOK, gin.H{"authenticated": false})
		}
	})

	// Test request without credentials
	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	// Should return 200 OK with authenticated: false
	assert.Equal(t, http.StatusOK, w.Code)
	assert.Contains(t, w.Body.String(), `"authenticated":false`)
}

func TestGetAuthContext(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.GET("/test", func(c *gin.Context) {
		// Test when no auth context is set
		authCtx, exists := GetAuthContext(c)
		assert.False(t, exists)
		assert.Nil(t, authCtx)

		// Set a mock auth context
		mockAuthCtx := &AuthContext{
			User: &entities.User{
				ID:    uuid.New(),
				Email: "<EMAIL>",
			},
			AuthType: "jwt",
		}
		c.Set("auth", mockAuthCtx)

		// Test when auth context is set
		authCtx, exists = GetAuthContext(c)
		assert.True(t, exists)
		assert.NotNil(t, authCtx)
		assert.Equal(t, "jwt", authCtx.AuthType)
		assert.Equal(t, "<EMAIL>", authCtx.User.Email)

		c.JSON(http.StatusOK, gin.H{"success": true})
	})

	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestGetCurrentUser(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.GET("/test", func(c *gin.Context) {
		// Test when no user is set
		user, exists := GetCurrentUser(c)
		assert.False(t, exists)
		assert.Nil(t, user)

		// Set a mock user
		mockUser := &entities.User{
			ID:    uuid.New(),
			Email: "<EMAIL>",
		}
		c.Set("user", mockUser)

		// Test when user is set
		user, exists = GetCurrentUser(c)
		assert.True(t, exists)
		assert.NotNil(t, user)
		assert.Equal(t, "<EMAIL>", user.Email)

		c.JSON(http.StatusOK, gin.H{"success": true})
	})

	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}

func TestGetCurrentUserID(t *testing.T) {
	gin.SetMode(gin.TestMode)

	router := gin.New()
	router.GET("/test", func(c *gin.Context) {
		// Test when no user ID is set
		userID, exists := GetCurrentUserID(c)
		assert.False(t, exists)
		assert.Empty(t, userID)

		// Set a mock user ID
		mockUserID := uuid.New().String()
		c.Set("user_id", mockUserID)

		// Test when user ID is set
		userID, exists = GetCurrentUserID(c)
		assert.True(t, exists)
		assert.Equal(t, mockUserID, userID)

		c.JSON(http.StatusOK, gin.H{"success": true})
	})

	req, _ := http.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	assert.Equal(t, http.StatusOK, w.Code)
}
