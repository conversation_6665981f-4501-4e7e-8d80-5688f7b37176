package entities

import (
	"time"

	"github.com/google/uuid"
)

// UsersOrganization represents simple membership between users and organizations
// This is purely for tracking WHO belongs to WHICH organization
// Permissions are handled separately in the Permission entity
type UsersOrganization struct {
	UserID         uuid.UUID  `json:"user_id" gorm:"type:uuid;primaryKey"`
	OrganizationID uuid.UUID  `json:"organization_id" gorm:"type:uuid;primaryKey"`
	JoinedAt       time.Time  `json:"joined_at" gorm:"not null;default:CURRENT_TIMESTAMP"`
	InvitedBy      *uuid.UUID `json:"invited_by,omitempty" gorm:"type:uuid"`

	// Relations
	User         User         `json:"user" gorm:"foreignKey:UserID"`
	Organization Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	Inviter      *User        `json:"inviter,omitempty" gorm:"foreignKey:InvitedBy"`
}

// TableName overrides the table name used by GORM
func (UsersOrganization) TableName() string {
	return "users_organizations"
}
