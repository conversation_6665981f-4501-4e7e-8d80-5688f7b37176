package entities

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Machine represents a device/computer that can run licenses (Ruby: Machine model)
// Maps from Ruby Machine model with full relationships and business logic
// Renamed account_id to organization_id, removed group fields for simplification
type Machine struct {
	// === CORE FIELDS (Ruby: primary attributes) ===
	ID        uuid.UUID  `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	LicenseID uuid.UUID  `json:"license_id" gorm:"type:uuid;not null;index"`
	PolicyID  uuid.UUID  `json:"policy_id" gorm:"type:uuid;not null;index"`
	OwnerID   *uuid.UUID `json:"owner_id,omitempty" gorm:"type:uuid;index"` // Optional user who owns this machine

	// === MACHINE IDENTIFICATION (Ruby: identification fields) ===
	Fingerprint string  `json:"fingerprint" gorm:"not null;index"` // Ruby: fingerprint (unique per license)
	Name        *string `json:"name,omitempty"`                    // Ruby: name (optional display name)
	Hostname    *string `json:"hostname,omitempty"`                // Ruby: hostname (optional)
	Platform    *string `json:"platform,omitempty"`                // Ruby: platform (optional)

	// === NETWORK INFORMATION (Ruby: network fields) ===
	IP *string `json:"ip,omitempty"` // Ruby: ip (IPv4/IPv6 address)

	// === MACHINE STATE (Ruby: state fields) ===
	Status        MachineStatus `json:"status" gorm:"type:varchar(50);default:'active'"` // Ruby: status (active/inactive)
	ActivatedAt   *time.Time    `json:"activated_at,omitempty"`                          // Ruby: activated_at
	DeactivatedAt *time.Time    `json:"deactivated_at,omitempty"`                        // Ruby: deactivated_at
	LastSeen      *time.Time    `json:"last_seen,omitempty"`                             // Ruby: last_seen

	// === HARDWARE TRACKING (Ruby: hardware fields) ===
	Cores *int `json:"cores,omitempty"` // Ruby: cores (CPU core count)

	// === COMPONENT FINGERPRINTING (Ruby: components association) ===
	Components []MachineComponent `json:"components,omitempty" gorm:"type:jsonb"` // Ruby: has_many :components (simplified as JSONB)

	// === HEARTBEAT TRACKING (Ruby: heartbeat fields) ===
	LastHeartbeatAt      *time.Time `json:"last_heartbeat_at,omitempty"`        // Ruby: last_heartbeat_at
	NextHeartbeatAt      *time.Time `json:"next_heartbeat_at,omitempty"`        // Ruby: calculated field
	LastDeathEventSentAt *time.Time `json:"last_death_event_sent_at,omitempty"` // Ruby: last_death_event_sent_at
	HeartbeatJID         *string    `json:"heartbeat_jid,omitempty"`            // Ruby: heartbeat_jid (job ID)

	// === CHECK-OUT TRACKING (Ruby: check-out fields) ===
	LastCheckOutAt *time.Time `json:"last_check_out_at,omitempty"` // Ruby: last_check_out_at (for floating licenses)

	// === PROCESS OVERRIDE (Ruby: max_processes_override field) ===
	MaxProcessesOverride *int `json:"max_processes_override,omitempty"` // Ruby: max_processes_override

	// === METADATA (Ruby: jsonb metadata) ===
	Metadata map[string]any `json:"metadata,omitempty" gorm:"type:jsonb"`

	// === AUDIT FIELDS (Ruby: timestamps) ===
	CreatedAt time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"index"`

	// === RELATIONSHIPS (Ruby: associations for eager loading) ===
	License License `json:"license" gorm:"foreignKey:LicenseID"`
	Policy  Policy  `json:"policy" gorm:"foreignKey:PolicyID"`
	Owner   *User   `json:"owner" gorm:"foreignKey:OwnerID"`

	// === PRIVATE FIELDS (Ruby: attr_accessor fields) ===
	// heartbeatStatusOverride is used for temporary status overrides (Ruby: attr_accessor :heartbeat_status_override)
	heartbeatStatusOverride HeartbeatStatus `json:"-" gorm:"-"`
}

// TableName specifies the table name for GORM
func (Machine) TableName() string {
	return "machines"
}

// ===== BASIC PROPERTY METHODS (Ruby: basic property methods) =====

// IsActive checks if machine is active (Ruby: status == 'active')
func (m *Machine) IsActive() bool {
	return m.Status == MachineStatusActive
}

// IsInactive checks if machine is inactive (Ruby: status == 'inactive')
func (m *Machine) IsInactive() bool {
	return m.Status == MachineStatusInactive
}

// HasOwner checks if machine has an owner (Ruby: owner.present?)
func (m *Machine) HasOwner() bool {
	return m.OwnerID != nil
}

// HasName checks if machine has a name (Ruby: name.present?)
func (m *Machine) HasName() bool {
	return m.Name != nil && *m.Name != ""
}

// HasHostname checks if machine has a hostname (Ruby: hostname.present?)
func (m *Machine) HasHostname() bool {
	return m.Hostname != nil && *m.Hostname != ""
}

// HasPlatform checks if machine has a platform (Ruby: platform.present?)
func (m *Machine) HasPlatform() bool {
	return m.Platform != nil && *m.Platform != ""
}

// HasIP checks if machine has an IP address (Ruby: ip.present?)
func (m *Machine) HasIP() bool {
	return m.IP != nil && *m.IP != ""
}

// HasCores checks if machine has cores specified (Ruby: cores.present?)
func (m *Machine) HasCores() bool {
	return m.Cores != nil && *m.Cores > 0
}

// GetCores returns the core count or 0 if not specified (Ruby: cores.to_i)
func (m *Machine) GetCores() int {
	if m.Cores == nil {
		return 0
	}
	return *m.Cores
}

// ===== MAX PROCESSES METHODS (Ruby: max_processes methods) =====

// GetMaxProcesses returns max processes from override or license (Ruby: max_processes method)
func (m *Machine) GetMaxProcesses() *int {
	if m.MaxProcessesOverride != nil {
		return m.MaxProcessesOverride
	}
	// TODO: Fix this when License entity is updated with MaxProcessesOverride field
	// if m.License.MaxProcessesOverride != nil {
	//     return m.License.MaxProcessesOverride
	// }
	return m.Policy.MaxProcesses
}

// HasMaxProcesses checks if machine has max processes limit (Ruby: max_processes?)
func (m *Machine) HasMaxProcesses() bool {
	maxProcesses := m.GetMaxProcesses()
	return maxProcesses != nil && *maxProcesses > 0
}

// HasMaxProcessesOverride checks if machine has max processes override (Ruby: max_processes_override?)
func (m *Machine) HasMaxProcessesOverride() bool {
	return m.MaxProcessesOverride != nil
}

// SetMaxProcesses sets the max processes override (Ruby: max_processes= method)
func (m *Machine) SetMaxProcesses(value *int) {
	m.MaxProcessesOverride = value
}

// ===== HEARTBEAT METHODS (Ruby: heartbeat methods) =====

// HeartbeatDuration returns the heartbeat duration from policy or default (Ruby: heartbeat_duration method)
func (m *Machine) HeartbeatDuration() time.Duration {
	if m.Policy.HeartbeatDuration != nil {
		return time.Duration(*m.Policy.HeartbeatDuration) * time.Second
	}
	return DefaultHeartbeatTTL
}

// IsHeartbeatMonitored checks if machine is being monitored (Ruby: heartbeat_monitored? / monitored?)
func (m *Machine) IsHeartbeatMonitored() bool {
	return m.HeartbeatJID != nil && *m.HeartbeatJID != ""
}

// IsMonitored is an alias for IsHeartbeatMonitored (Ruby: alias_method :monitored?, :heartbeat_monitored?)
func (m *Machine) IsMonitored() bool {
	return m.IsHeartbeatMonitored()
}

// RequiresHeartbeat checks if machine requires heartbeat (Ruby: requires_heartbeat? method)
func (m *Machine) RequiresHeartbeat() bool {
	if m.Policy.RequireHeartbeat {
		return true
	}
	return m.LastHeartbeatAt != nil
}

// HasHeartbeat checks if machine has heartbeat (Ruby: heartbeat? method)
func (m *Machine) HasHeartbeat() bool {
	return m.GetNextHeartbeatAt() != nil
}

// GetNextHeartbeatAt calculates next heartbeat time (Ruby: next_heartbeat_at method)
func (m *Machine) GetNextHeartbeatAt() *time.Time {
	if m.LastHeartbeatAt == nil {
		return nil
	}
	nextTime := m.LastHeartbeatAt.Add(m.HeartbeatDuration())
	return &nextTime
}

// GetHeartbeatStatus calculates current heartbeat status (Ruby: heartbeat_status method)
func (m *Machine) GetHeartbeatStatus() HeartbeatStatus {
	// Check for status override first (Ruby: heartbeat_status_override.present?)
	if m.heartbeatStatusOverride != "" {
		return m.heartbeatStatusOverride
	}

	// If no heartbeat and either doesn't require heartbeat or is within grace period
	if !m.HasHeartbeat() && (!m.RequiresHeartbeat() || m.CreatedAt.After(time.Now().Add(-m.HeartbeatDuration()))) {
		return HeartbeatStatusNotStarted
	}

	// If has heartbeat and next heartbeat is in the future
	if m.HasHeartbeat() {
		nextHeartbeat := m.GetNextHeartbeatAt()
		if nextHeartbeat != nil && nextHeartbeat.After(time.Now()) {
			return HeartbeatStatusAlive
		}
	}

	// Otherwise machine is dead
	return HeartbeatStatusDead
}

// GetStatus is an alias for GetHeartbeatStatus (Ruby: alias_method :status, :heartbeat_status)
func (m *Machine) GetStatus() HeartbeatStatus {
	return m.GetHeartbeatStatus()
}

// IsHeartbeatNotStarted checks if heartbeat is not started (Ruby: heartbeat_not_started? / not_started?)
func (m *Machine) IsHeartbeatNotStarted() bool {
	return m.GetHeartbeatStatus() == HeartbeatStatusNotStarted
}

// IsNotStarted is an alias for IsHeartbeatNotStarted (Ruby: alias_method :not_started?, :heartbeat_not_started?)
func (m *Machine) IsNotStarted() bool {
	return m.IsHeartbeatNotStarted()
}

// IsHeartbeatAlive checks if heartbeat is alive (Ruby: heartbeat_alive? / alive?)
func (m *Machine) IsHeartbeatAlive() bool {
	status := m.GetHeartbeatStatus()
	return status == HeartbeatStatusAlive || status == HeartbeatStatusResurrected
}

// IsAlive is an alias for IsHeartbeatAlive (Ruby: alias_method :alive?, :heartbeat_alive?)
func (m *Machine) IsAlive() bool {
	return m.IsHeartbeatAlive()
}

// IsHeartbeatDead checks if heartbeat is dead (Ruby: heartbeat_dead? / dead?)
func (m *Machine) IsHeartbeatDead() bool {
	return m.GetHeartbeatStatus() == HeartbeatStatusDead
}

// IsDead is an alias for IsHeartbeatDead (Ruby: alias_method :dead?, :heartbeat_dead?)
func (m *Machine) IsDead() bool {
	return m.IsHeartbeatDead()
}

// IsHeartbeatOk checks if heartbeat is ok (Ruby: heartbeat_ok? / ok?)
func (m *Machine) IsHeartbeatOk() bool {
	return m.IsHeartbeatNotStarted() || m.IsHeartbeatAlive()
}

// IsOk is an alias for IsHeartbeatOk (Ruby: alias_method :ok?, :heartbeat_ok?)
func (m *Machine) IsOk() bool {
	return m.IsHeartbeatOk()
}

// SetHeartbeatStatusOverride sets temporary status override (Ruby: self.heartbeat_status_override = value)
func (m *Machine) SetHeartbeatStatusOverride(status HeartbeatStatus) {
	m.heartbeatStatusOverride = status
}

// ClearHeartbeatStatusOverride clears the status override (Ruby: self.heartbeat_status_override = nil)
func (m *Machine) ClearHeartbeatStatusOverride() {
	m.heartbeatStatusOverride = ""
}

// ===== HEARTBEAT ACTION METHODS (Ruby: heartbeat action methods) =====

// Ping updates the last heartbeat time (Ruby: ping! method)
func (m *Machine) Ping() error {
	now := time.Now()
	m.LastHeartbeatAt = &now
	m.HeartbeatJID = nil
	return nil
}

// Reset clears the heartbeat data (Ruby: reset! method)
func (m *Machine) Reset() error {
	m.LastHeartbeatAt = nil
	m.HeartbeatJID = nil
	return nil
}

// Resurrect brings a dead machine back to life (Ruby: resurrect! method)
func (m *Machine) Resurrect() error {
	if !m.CanResurrectDead() {
		return ErrResurrectionUnsupported
	}

	if m.IsResurrectionPeriodPassed() {
		return ErrResurrectionExpired
	}

	now := time.Now()
	m.LastHeartbeatAt = &now
	m.LastDeathEventSentAt = nil
	m.HeartbeatJID = nil
	m.SetHeartbeatStatusOverride(HeartbeatStatusResurrected)

	return nil
}

// ===== RESURRECTION LOGIC METHODS (Ruby: resurrection logic methods) =====

// CanResurrectDead checks if machine can be resurrected (Ruby: resurrect_dead? method)
func (m *Machine) CanResurrectDead() bool {
	return m.Policy.ResurrectsDeadMachines()
}

// CanAlwaysResurrectDead checks if machine can always be resurrected (Ruby: always_resurrect_dead? method)
func (m *Machine) CanAlwaysResurrectDead() bool {
	return m.Policy.AlwaysResurrectsDead()
}

// IsResurrectionPeriodPassed checks if resurrection period has passed (Ruby: resurrection_period_passed? method)
func (m *Machine) IsResurrectionPeriodPassed() bool {
	if m.CanAlwaysResurrectDead() {
		return false
	}

	if !m.RequiresHeartbeat() || !m.CanResurrectDead() || !m.HasHeartbeat() {
		return true
	}

	nextHeartbeat := m.GetNextHeartbeatAt()
	if nextHeartbeat == nil {
		return true
	}

	lazarusTTL := m.GetLazarusTTL()
	return time.Now().After(nextHeartbeat.Add(lazarusTTL))
}

// GetLazarusTTL returns the lazarus TTL duration (Ruby: lazarus_ttl method)
func (m *Machine) GetLazarusTTL() time.Duration {
	ttlSeconds := m.Policy.GetLazarusTTL()
	if ttlSeconds > 0 {
		return time.Duration(ttlSeconds) * time.Second
	}
	return DefaultHeartbeatTTL // Default resurrection TTL
}

// ===== UNIQUENESS METHODS (Ruby: uniqueness methods) =====

// IsUniquePerOrganization checks if machine is unique per organization (Ruby: unique_per_account?)
func (m *Machine) IsUniquePerOrganization() bool {
	return m.Policy.MachineUniquePerOrganization()
}

// IsUniquePerProduct checks if machine is unique per product (Ruby: unique_per_product?)
func (m *Machine) IsUniquePerProduct() bool {
	return m.Policy.MachineUniquePerProduct()
}

// IsUniquePerPolicy checks if machine is unique per policy (Ruby: unique_per_policy?)
func (m *Machine) IsUniquePerPolicy() bool {
	return m.Policy.MachineUniquePerPolicy()
}

// IsUniquePerLicense checks if machine is unique per license (Ruby: unique_per_license?)
func (m *Machine) IsUniquePerLicense() bool {
	return m.Policy.MachineUniquePerLicense()
}

// ===== LEASING METHODS (Ruby: leasing methods) =====

// IsLeasePerLicense checks if machine lease is per license (Ruby: lease_per_license?)
func (m *Machine) IsLeasePerLicense() bool {
	return m.Policy.MachineLeasePerLicense()
}

// IsLeasePerUser checks if machine lease is per user (Ruby: lease_per_user?)
func (m *Machine) IsLeasePerUser() bool {
	return m.Policy.MachineLeasePerUser()
}

// ===== COMPONENT METHODS (Ruby: component methods) =====

// HasComponents checks if machine has components (Ruby: components.any?)
func (m *Machine) HasComponents() bool {
	return len(m.Components) > 0
}

// GetComponent returns component fingerprint by name (Ruby: components.find_by(name: name)&.fingerprint)
func (m *Machine) GetComponent(name string) string {
	for _, component := range m.Components {
		if component.Name == name {
			return component.Fingerprint
		}
	}
	return ""
}

// HasComponent checks if machine has a specific component (Ruby: components.exists?(name: name))
func (m *Machine) HasComponent(name string) bool {
	return m.GetComponent(name) != ""
}

// AddComponent adds a component to the machine (Ruby: components.create!)
func (m *Machine) AddComponent(name, fingerprint string) {
	// Remove existing component with same name
	m.RemoveComponent(name)

	// Add new component
	m.Components = append(m.Components, MachineComponent{
		Name:        name,
		Fingerprint: fingerprint,
	})
}

// RemoveComponent removes a component from the machine (Ruby: components.where(name: name).destroy_all)
func (m *Machine) RemoveComponent(name string) {
	var newComponents []MachineComponent
	for _, component := range m.Components {
		if component.Name != name {
			newComponents = append(newComponents, component)
		}
	}
	m.Components = newComponents
}

// GetComponentsFingerprint generates a combined fingerprint from all components (Ruby: components.pluck(:fingerprint).join)
func (m *Machine) GetComponentsFingerprint() string {
	if len(m.Components) == 0 {
		return ""
	}

	var fingerprints []string
	for _, component := range m.Components {
		fingerprints = append(fingerprints, component.Fingerprint)
	}

	// Sort for consistent fingerprint generation
	// Note: In Go, we'd typically use sort.Strings() but keeping it simple for now
	result := ""
	for i, fp := range fingerprints {
		if i > 0 {
			result += ","
		}
		result += fp
	}
	return result
}
