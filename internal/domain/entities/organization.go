package entities

import (
	"database/sql/driver"
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Organization struct {
	ID    uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name  string    `json:"name" gorm:"size:255;not null"`
	Slug  string    `json:"slug" gorm:"size:255;uniqueIndex;not null"`
	Email string    `json:"email" gorm:"size:255;uniqueIndex;not null"`

	// Organization type and status
	Type      string `json:"type" gorm:"size:50;default:'vendor';check:type IN ('vendor', 'reseller', 'customer')"`
	Status    string `json:"status" gorm:"size:50;default:'active';check:status IN ('active', 'suspended', 'canceled')"`
	Protected bool   `json:"protected" gorm:"default:false"`

	// Cryptographic keys (encrypted at rest)
	PublicKey         *string `json:"public_key,omitempty" gorm:"type:text"`
	<PERSON>Key        *string `json:"-" gorm:"type:text"` // RSA private key (encrypted)
	SecretKey         *string `json:"-" gorm:"type:text"` // 128-char hex secret
	Ed25519PrivateKey *string `json:"-" gorm:"type:text"` // Ed25519 private key (encrypted)
	Ed25519PublicKey  *string `json:"ed25519_public_key,omitempty" gorm:"type:text"`

	// Resource limits
	MaxUsers    *int `json:"max_users,omitempty" gorm:"type:integer"`
	MaxLicenses *int `json:"max_licenses,omitempty" gorm:"type:integer"`
	MaxMachines *int `json:"max_machines,omitempty" gorm:"type:integer"`

	// Configuration
	Settings OrganizationSettings `json:"settings" gorm:"type:jsonb;default:'{}'"`
	Metadata Metadata             `json:"metadata" gorm:"type:jsonb;default:'{}'"`

	// Timestamps
	CreatedAt time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at,omitempty" gorm:"index"`

	// Relations
	Users     []UsersOrganization `json:"users,omitempty" gorm:"foreignKey:OrganizationID"`
	Products  []Product           `json:"products,omitempty" gorm:"foreignKey:OrganizationID"`
	Licenses  []License           `json:"licenses,omitempty" gorm:"foreignKey:OrganizationID"`
	APITokens []APIToken          `json:"api_tokens,omitempty" gorm:"foreignKey:OrganizationID"`
}

// Organization types
const (
	OrganizationTypeVendor   = "vendor"
	OrganizationTypeReseller = "reseller"
	OrganizationTypeCustomer = "customer"
)

// Organization statuses
const (
	OrganizationStatusActive    = "active"
	OrganizationStatusSuspended = "suspended"
	OrganizationStatusCanceled  = "canceled"
)

type OrganizationSettings map[string]interface{}

// Scan implements the sql.Scanner interface for JSONB scanning
func (s *OrganizationSettings) Scan(value interface{}) error {
	if value == nil {
		*s = make(map[string]interface{})
		return nil
	}

	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into OrganizationSettings", value)
	}

	if len(data) == 0 {
		*s = make(map[string]interface{})
		return nil
	}

	return json.Unmarshal(data, s)
}

// Value implements the driver.Valuer interface for JSONB storage
func (s OrganizationSettings) Value() (driver.Value, error) {
	if s == nil {
		return "{}", nil
	}
	return json.Marshal(s)
}

// Metadata is a common type for entity metadata across all entities
type Metadata map[string]interface{}

// Scan implements the sql.Scanner interface for JSONB scanning
func (m *Metadata) Scan(value interface{}) error {
	if value == nil {
		*m = make(map[string]interface{})
		return nil
	}

	var data []byte
	switch v := value.(type) {
	case []byte:
		data = v
	case string:
		data = []byte(v)
	default:
		return fmt.Errorf("cannot scan %T into Metadata", value)
	}

	if len(data) == 0 {
		*m = make(map[string]interface{})
		return nil
	}

	return json.Unmarshal(data, m)
}

// Value implements the driver.Valuer interface for JSONB storage
func (m Metadata) Value() (driver.Value, error) {
	if m == nil {
		return "{}", nil
	}
	return json.Marshal(m)
}
