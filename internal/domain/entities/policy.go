package entities

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// Policy represents a license policy entity (Ruby: Policy model)
// Maps directly from Ruby Policy class with Go improvements and type safety
type Policy struct {
	// === CORE FIELDS (Ruby: primary attributes) ===
	ID        uuid.UUID `json:"id" gorm:"type:uuid;primary_key;default:gen_random_uuid()"`
	Name      string    `json:"name" gorm:"not null"`           // Ruby: validates :name, presence: true
	Duration  *int64    `json:"duration,omitempty"`             // Ruby: duration in seconds (nullable)
	Strict    bool      `json:"strict" gorm:"default:false"`    // Ruby: strict field
	Floating  bool      `json:"floating" gorm:"default:true"`   // Ruby: floating field (default true)
	Encrypted bool      `json:"encrypted" gorm:"default:false"` // Ruby: encrypted field
	Protected bool      `json:"protected" gorm:"default:false"` // Ruby: protected field
	UsePool   bool      `json:"use_pool" gorm:"default:false"`  // Ruby: use_pool field

	// === ASSOCIATIONS (Ruby: belongs_to) ===
	OrganizationID uuid.UUID `json:"organization_id" gorm:"column:organization_id;type:uuid;not null;index"` // Ruby: account_id -> organization_id
	ProductID      uuid.UUID `json:"product_id" gorm:"column:product_id;type:uuid;not null;index"`           // Ruby: belongs_to :product

	// === CRYPTO SCHEME (Ruby: scheme field) ===
	Scheme *CryptoScheme `json:"scheme,omitempty" gorm:"type:varchar(50)"` // Ruby: scheme field with validation

	// === RESOURCE LIMITS (Ruby: max_* fields) ===
	MaxMachines  *int `json:"max_machines,omitempty"`  // Ruby: max_machines with validation
	MaxCores     *int `json:"max_cores,omitempty"`     // Ruby: max_cores with validation
	MaxUses      *int `json:"max_uses,omitempty"`      // Ruby: max_uses with validation
	MaxProcesses *int `json:"max_processes,omitempty"` // Ruby: max_processes with validation
	MaxUsers     *int `json:"max_users,omitempty"`     // Ruby: max_users with validation

	// === HEARTBEAT CONFIGURATION (Ruby: heartbeat fields) ===
	RequireHeartbeat  bool            `json:"require_heartbeat" gorm:"default:false"`            // Ruby: require_heartbeat
	HeartbeatDuration *int            `json:"heartbeat_duration,omitempty"`                      // Ruby: heartbeat_duration in seconds
	HeartbeatBasis    *HeartbeatBasis `json:"heartbeat_basis,omitempty" gorm:"type:varchar(50)"` // Ruby: heartbeat_basis

	// === CHECK-IN CONFIGURATION (Ruby: check-in fields) ===
	RequireCheckIn       bool    `json:"require_check_in" gorm:"default:false"` // Ruby: require_check_in
	CheckInInterval      *string `json:"check_in_interval,omitempty"`           // Ruby: check_in_interval (day/week/month/year)
	CheckInIntervalCount *int    `json:"check_in_interval_count,omitempty"`     // Ruby: check_in_interval_count (1-365)

	// === SCOPE REQUIREMENTS (Ruby: require_*_scope fields) ===
	// These fields control whether specific scopes are required during license validation
	RequireProductScope     bool `json:"require_product_scope" gorm:"default:false"`     // Ruby: require_product_scope
	RequirePolicyScope      bool `json:"require_policy_scope" gorm:"default:false"`      // Ruby: require_policy_scope
	RequireUserScope        bool `json:"require_user_scope" gorm:"default:false"`        // Ruby: require_user_scope
	RequireMachineScope     bool `json:"require_machine_scope" gorm:"default:false"`     // Ruby: require_machine_scope
	RequireFingerprintScope bool `json:"require_fingerprint_scope" gorm:"default:false"` // Ruby: require_fingerprint_scope
	RequireComponentsScope  bool `json:"require_components_scope" gorm:"default:false"`  // Ruby: require_components_scope
	RequireChecksumScope    bool `json:"require_checksum_scope" gorm:"default:false"`    // Ruby: require_checksum_scope
	RequireVersionScope     bool `json:"require_version_scope" gorm:"default:false"`     // Ruby: require_version_scope
	// Note: RequireEnvironmentScope intentionally omitted per Go implementation requirements

	// === STRATEGY FIELDS (Ruby: strategy fields with custom types) ===
	MachineUniquenessStrategy     *MachineUniquenessStrategy     `json:"machine_uniqueness_strategy,omitempty" gorm:"column:machine_uniqueness_strategy;type:varchar(255)"`
	MachineMatchingStrategy       *MatchingStrategy              `json:"machine_matching_strategy,omitempty" gorm:"column:machine_matching_strategy;type:varchar(255)"`
	ComponentUniquenessStrategy   *ComponentUniquenessStrategy   `json:"component_uniqueness_strategy,omitempty" gorm:"column:component_uniqueness_strategy;type:varchar(255)"`
	ComponentMatchingStrategy     *MatchingStrategy              `json:"component_matching_strategy,omitempty" gorm:"column:component_matching_strategy;type:varchar(255)"`
	ExpirationStrategy            *ExpirationStrategy            `json:"expiration_strategy,omitempty" gorm:"column:expiration_strategy;type:varchar(255)"`
	ExpirationBasis               *ExpirationBasis               `json:"expiration_basis,omitempty" gorm:"column:expiration_basis;type:varchar(255)"`
	RenewalBasis                  *RenewalBasis                  `json:"renewal_basis,omitempty" gorm:"column:renewal_basis;type:varchar(255)"`
	TransferStrategy              *TransferStrategy              `json:"transfer_strategy,omitempty" gorm:"column:transfer_strategy;type:varchar(255)"`
	AuthenticationStrategy        *AuthenticationStrategy        `json:"authentication_strategy,omitempty" gorm:"column:authentication_strategy;type:varchar(255)"`
	OverageStrategy               *OverageStrategy               `json:"overage_strategy,omitempty" gorm:"column:overage_strategy;type:varchar(255)"`
	HeartbeatCullStrategy         *HeartbeatCullStrategy         `json:"heartbeat_cull_strategy,omitempty" gorm:"column:heartbeat_cull_strategy;type:varchar(255)"`
	HeartbeatResurrectionStrategy *HeartbeatResurrectionStrategy `json:"heartbeat_resurrection_strategy,omitempty" gorm:"column:heartbeat_resurrection_strategy;type:varchar(255)"`
	MachineLeasing                *LeasingStrategy               `json:"machine_leasing_strategy,omitempty" gorm:"column:machine_leasing_strategy;type:varchar(255)"`
	ProcessLeasing                *LeasingStrategy               `json:"process_leasing_strategy,omitempty" gorm:"column:process_leasing_strategy;type:varchar(255)"`

	// === METADATA (Ruby: jsonb metadata) ===
	Metadata map[string]any `json:"metadata,omitempty" gorm:"type:jsonb"`

	// === AUDIT FIELDS (Ruby: timestamps) ===
	CreatedAt time.Time      `json:"created_at" gorm:"column:created_at;not null"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"column:updated_at;not null"`
	DeletedAt gorm.DeletedAt `json:"deleted_at" gorm:"column:deleted_at;index"`

	// === RELATIONSHIPS (Ruby: associations for eager loading) ===
	Organization Organization `json:"organization" gorm:"foreignKey:OrganizationID"`
	Product      Product      `json:"product" gorm:"foreignKey:ProductID"`
	Licenses     []License    `json:"licenses" gorm:"foreignKey:PolicyID"`
	Machines     []Machine    `json:"machines" gorm:"foreignKey:PolicyID"`
}

// TableName returns the table name for Policy
func (Policy) TableName() string {
	return "policies"
}

// ===== BASIC PROPERTY METHODS (Ruby: basic property checks) =====

// IsPool checks if policy uses a pool (Ruby: pool?)
func (p *Policy) IsPool() bool {
	return p.UsePool
}

// IsStrict checks if policy is strict (Ruby: strict?)
func (p *Policy) IsStrict() bool {
	return p.Strict
}

// IsFloating checks if policy is floating (Ruby: floating?)
func (p *Policy) IsFloating() bool {
	return p.Floating
}

// IsNodeLocked checks if policy is node-locked (Ruby: node_locked?)
func (p *Policy) IsNodeLocked() bool {
	return !p.Floating
}

// IsEncrypted checks if policy is encrypted (Ruby: encrypted?)
func (p *Policy) IsEncrypted() bool {
	return p.Encrypted
}

// IsProtected checks if policy is protected (Ruby: protected?)
func (p *Policy) IsProtected() bool {
	return p.Protected
}

// RequiresCheckIn checks if policy requires check-in (Ruby: requires_check_in?)
func (p *Policy) RequiresCheckIn() bool {
	return p.RequireCheckIn
}

// HasScheme checks if policy has a crypto scheme (Ruby: scheme?)
func (p *Policy) HasScheme() bool {
	return p.Scheme != nil
}

// ===== HEARTBEAT METHODS (Ruby: heartbeat strategy methods) =====

// DeactivatesDead checks if policy deactivates dead machines (Ruby: deactivate_dead?)
func (p *Policy) DeactivatesDead() bool {
	if p.HeartbeatCullStrategy == nil {
		return true // Default behavior for backwards compatibility
	}
	return *p.HeartbeatCullStrategy == HeartbeatDeactivateDead
}

// KeepsDead checks if policy keeps dead machines (Ruby: keep_dead?)
func (p *Policy) KeepsDead() bool {
	return p.HeartbeatCullStrategy != nil && *p.HeartbeatCullStrategy == HeartbeatKeepDead
}

// ResurrectsDeadMachines checks if policy resurrects dead machines (Ruby: resurrect_dead?)
func (p *Policy) ResurrectsDeadMachines() bool {
	if p.HeartbeatResurrectionStrategy == nil {
		return false // Default behavior for backwards compatibility
	}
	return *p.HeartbeatResurrectionStrategy != HeartbeatNoRevive
}

// AlwaysResurrectsDead checks if policy always resurrects dead machines (Ruby: always_resurrect_dead?)
func (p *Policy) AlwaysResurrectsDead() bool {
	return p.HeartbeatResurrectionStrategy != nil && *p.HeartbeatResurrectionStrategy == HeartbeatAlwaysRevive
}

// RequiresHeartbeat checks if policy requires heartbeat (Ruby: require_heartbeat?)
func (p *Policy) RequiresHeartbeat() bool {
	return p.RequireHeartbeat
}

// HasHeartbeatDuration checks if policy has heartbeat duration set (Ruby: heartbeat_duration?)
func (p *Policy) HasHeartbeatDuration() bool {
	return p.HeartbeatDuration != nil && *p.HeartbeatDuration > 0
}

// GetHeartbeatDuration returns heartbeat duration as time.Duration (Ruby: heartbeat_duration)
func (p *Policy) GetHeartbeatDuration() time.Duration {
	if p.HeartbeatDuration == nil {
		return 0
	}
	return time.Duration(*p.HeartbeatDuration) * time.Second
}

// GetLazarusTTL returns the resurrection TTL in seconds (Ruby: lazarus_ttl)
func (p *Policy) GetLazarusTTL() int {
	if p.HeartbeatResurrectionStrategy == nil {
		return 0
	}
	return p.HeartbeatResurrectionStrategy.GetTTLSeconds()
}

// HeartbeatFromCreation checks if heartbeat starts from creation (Ruby: heartbeat_from_creation?)
func (p *Policy) HeartbeatFromCreation() bool {
	return p.HeartbeatBasis != nil && *p.HeartbeatBasis == HeartbeatFromCreation
}

// HeartbeatFromFirstPing checks if heartbeat starts from first ping (Ruby: heartbeat_from_first_ping?)
func (p *Policy) HeartbeatFromFirstPing() bool {
	if p.HeartbeatBasis == nil {
		return true // Default behavior for backwards compatibility
	}
	return *p.HeartbeatBasis == HeartbeatFromFirstPing
}

// ===== MACHINE UNIQUENESS METHODS (Ruby: machine uniqueness strategy methods) =====

// GetMachineUniquenessStrategy returns machine uniqueness strategy with default (Ruby: machine_uniqueness_strategy)
func (p *Policy) GetMachineUniquenessStrategy() MachineUniquenessStrategy {
	if p.MachineUniquenessStrategy == nil {
		return MachineUniquePerLicense // Default for backwards compatibility
	}
	return *p.MachineUniquenessStrategy
}

// MachineUniquePerOrganization checks if machines are unique per organization (Ruby: machine_unique_per_account?)
func (p *Policy) MachineUniquePerOrganization() bool {
	return p.GetMachineUniquenessStrategy() == MachineUniquePerOrganization
}

// MachineUniquePerProduct checks if machines are unique per product (Ruby: machine_unique_per_product?)
func (p *Policy) MachineUniquePerProduct() bool {
	return p.GetMachineUniquenessStrategy() == MachineUniquePerProduct
}

// MachineUniquePerPolicy checks if machines are unique per policy (Ruby: machine_unique_per_policy?)
func (p *Policy) MachineUniquePerPolicy() bool {
	return p.GetMachineUniquenessStrategy() == MachineUniquePerPolicy
}

// MachineUniquePerLicense checks if machines are unique per license (Ruby: machine_unique_per_license?)
func (p *Policy) MachineUniquePerLicense() bool {
	return p.GetMachineUniquenessStrategy() == MachineUniquePerLicense
}

// GetMachineUniquenessRank returns machine uniqueness strategy rank (Ruby: machine_uniqueness_strategy_rank)
func (p *Policy) GetMachineUniquenessRank() int {
	return p.GetMachineUniquenessStrategy().Rank()
}

// ===== COMPONENT UNIQUENESS METHODS (Ruby: component uniqueness strategy methods) =====

// GetComponentUniquenessStrategy returns component uniqueness strategy with default (Ruby: component_uniqueness_strategy)
func (p *Policy) GetComponentUniquenessStrategy() ComponentUniquenessStrategy {
	if p.ComponentUniquenessStrategy == nil {
		return ComponentUniquePerMachine // Default for backwards compatibility
	}
	return *p.ComponentUniquenessStrategy
}

// ComponentUniquePerOrganization checks if components are unique per organization (Ruby: component_unique_per_account?)
func (p *Policy) ComponentUniquePerOrganization() bool {
	return p.GetComponentUniquenessStrategy() == ComponentUniquePerOrganization
}

// ComponentUniquePerProduct checks if components are unique per product (Ruby: component_unique_per_product?)
func (p *Policy) ComponentUniquePerProduct() bool {
	return p.GetComponentUniquenessStrategy() == ComponentUniquePerProduct
}

// ComponentUniquePerPolicy checks if components are unique per policy (Ruby: component_unique_per_policy?)
func (p *Policy) ComponentUniquePerPolicy() bool {
	return p.GetComponentUniquenessStrategy() == ComponentUniquePerPolicy
}

// ComponentUniquePerLicense checks if components are unique per license (Ruby: component_unique_per_license?)
func (p *Policy) ComponentUniquePerLicense() bool {
	return p.GetComponentUniquenessStrategy() == ComponentUniquePerLicense
}

// ComponentUniquePerMachine checks if components are unique per machine (Ruby: component_unique_per_machine?)
func (p *Policy) ComponentUniquePerMachine() bool {
	return p.GetComponentUniquenessStrategy() == ComponentUniquePerMachine
}

// GetComponentUniquenessRank returns component uniqueness strategy rank (Ruby: component_uniqueness_strategy_rank)
func (p *Policy) GetComponentUniquenessRank() int {
	return p.GetComponentUniquenessStrategy().Rank()
}

// ===== MACHINE MATCHING METHODS (Ruby: machine matching strategy methods) =====

// GetMachineMatchingStrategy returns machine matching strategy with default (Ruby: machine_matching_strategy)
func (p *Policy) GetMachineMatchingStrategy() MatchingStrategy {
	if p.MachineMatchingStrategy == nil {
		return MatchAny // Default for backwards compatibility
	}
	return *p.MachineMatchingStrategy
}

// MachineMatchAny checks if machine matches any component (Ruby: machine_match_any?)
func (p *Policy) MachineMatchAny() bool {
	return p.GetMachineMatchingStrategy() == MatchAny
}

// MachineMatchTwo checks if machine matches two components (Ruby: machine_match_two?)
func (p *Policy) MachineMatchTwo() bool {
	return p.GetMachineMatchingStrategy() == MatchTwo
}

// MachineMatchMost checks if machine matches most components (Ruby: machine_match_most?)
func (p *Policy) MachineMatchMost() bool {
	return p.GetMachineMatchingStrategy() == MatchMost
}

// MachineMatchAll checks if machine matches all components (Ruby: machine_match_all?)
func (p *Policy) MachineMatchAll() bool {
	return p.GetMachineMatchingStrategy() == MatchAll
}

// ===== COMPONENT MATCHING METHODS (Ruby: component matching strategy methods) =====

// GetComponentMatchingStrategy returns component matching strategy with default (Ruby: component_matching_strategy)
func (p *Policy) GetComponentMatchingStrategy() MatchingStrategy {
	if p.ComponentMatchingStrategy == nil {
		return MatchAny // Default for backwards compatibility
	}
	return *p.ComponentMatchingStrategy
}

// ComponentMatchAny checks if component matches any (Ruby: component_match_any?)
func (p *Policy) ComponentMatchAny() bool {
	return p.GetComponentMatchingStrategy() == MatchAny
}

// ComponentMatchTwo checks if component matches two (Ruby: component_match_two?)
func (p *Policy) ComponentMatchTwo() bool {
	return p.GetComponentMatchingStrategy() == MatchTwo
}

// ComponentMatchMost checks if component matches most (Ruby: component_match_most?)
func (p *Policy) ComponentMatchMost() bool {
	return p.GetComponentMatchingStrategy() == MatchMost
}

// ComponentMatchAll checks if component matches all (Ruby: component_match_all?)
func (p *Policy) ComponentMatchAll() bool {
	return p.GetComponentMatchingStrategy() == MatchAll
}

// ===== EXPIRATION STRATEGY METHODS (Ruby: expiration strategy methods) =====

// GetExpirationStrategy returns expiration strategy with default (Ruby: expiration_strategy)
func (p *Policy) GetExpirationStrategy() ExpirationStrategy {
	if p.ExpirationStrategy == nil {
		return ExpirationRestrictAccess // Default for backwards compatibility
	}
	return *p.ExpirationStrategy
}

// RestrictsAccess checks if policy restricts access on expiry (Ruby: restrict_access?)
func (p *Policy) RestrictsAccess() bool {
	return p.GetExpirationStrategy() == ExpirationRestrictAccess
}

// RevokesAccess checks if policy revokes access on expiry (Ruby: revoke_access?)
func (p *Policy) RevokesAccess() bool {
	return p.GetExpirationStrategy() == ExpirationRevokeAccess
}

// MaintainsAccess checks if policy maintains access on expiry (Ruby: maintain_access?)
func (p *Policy) MaintainsAccess() bool {
	return p.GetExpirationStrategy() == ExpirationMaintainAccess
}

// AllowsAccess checks if policy allows access on expiry (Ruby: allow_access?)
func (p *Policy) AllowsAccess() bool {
	return p.GetExpirationStrategy() == ExpirationAllowAccess
}

// ===== EXPIRATION BASIS METHODS (Ruby: expiration basis methods) =====

// GetExpirationBasis returns expiration basis with default (Ruby: expiration_basis)
func (p *Policy) GetExpirationBasis() ExpirationBasis {
	if p.ExpirationBasis == nil {
		return ExpirationFromCreation // Default for backwards compatibility
	}
	return *p.ExpirationBasis
}

// ExpiresFromCreation checks if expiry starts from creation (Ruby: expire_from_creation?)
func (p *Policy) ExpiresFromCreation() bool {
	return p.GetExpirationBasis() == ExpirationFromCreation
}

// ExpiresFromFirstValidation checks if expiry starts from first validation (Ruby: expire_from_first_validation?)
func (p *Policy) ExpiresFromFirstValidation() bool {
	return p.GetExpirationBasis() == ExpirationFromFirstValidation
}

// ExpiresFromFirstActivation checks if expiry starts from first activation (Ruby: expire_from_first_activation?)
func (p *Policy) ExpiresFromFirstActivation() bool {
	return p.GetExpirationBasis() == ExpirationFromFirstActivation
}

// ExpiresFromFirstUse checks if expiry starts from first use (Ruby: expire_from_first_use?)
func (p *Policy) ExpiresFromFirstUse() bool {
	return p.GetExpirationBasis() == ExpirationFromFirstUse
}

// ExpiresFromFirstDownload checks if expiry starts from first download (Ruby: expire_from_first_download?)
func (p *Policy) ExpiresFromFirstDownload() bool {
	return p.GetExpirationBasis() == ExpirationFromFirstDownload
}

// ===== RENEWAL BASIS METHODS (Ruby: renewal basis methods) =====

// GetRenewalBasis returns renewal basis with default (Ruby: renewal_basis)
func (p *Policy) GetRenewalBasis() RenewalBasis {
	if p.RenewalBasis == nil {
		return RenewalFromExpiry // Default for backwards compatibility
	}
	return *p.RenewalBasis
}

// RenewsFromExpiry checks if renewal starts from expiry (Ruby: renew_from_expiry?)
func (p *Policy) RenewsFromExpiry() bool {
	return p.GetRenewalBasis() == RenewalFromExpiry
}

// RenewsFromNow checks if renewal starts from now (Ruby: renew_from_now?)
func (p *Policy) RenewsFromNow() bool {
	return p.GetRenewalBasis() == RenewalFromNow
}

// RenewsFromNowIfExpired checks if renewal starts from now if expired (Ruby: renew_from_now_if_expired?)
func (p *Policy) RenewsFromNowIfExpired() bool {
	return p.GetRenewalBasis() == RenewalFromNowIfExpired
}

// ===== AUTHENTICATION STRATEGY METHODS (Ruby: authentication strategy methods) =====

// GetAuthenticationStrategy returns authentication strategy with default (Ruby: authentication_strategy)
func (p *Policy) GetAuthenticationStrategy() AuthenticationStrategy {
	if p.AuthenticationStrategy == nil {
		return AuthToken // Default for backwards compatibility
	}
	return *p.AuthenticationStrategy
}

// SupportsTokenAuth checks if policy supports token authentication (Ruby: supports_token_auth?)
func (p *Policy) SupportsTokenAuth() bool {
	return p.GetAuthenticationStrategy().SupportsTokenAuth()
}

// SupportsLicenseAuth checks if policy supports license authentication (Ruby: supports_license_auth?)
func (p *Policy) SupportsLicenseAuth() bool {
	return p.GetAuthenticationStrategy().SupportsLicenseAuth()
}

// SupportsSessionAuth checks if policy supports session authentication (Ruby: supports_session_auth?)
func (p *Policy) SupportsSessionAuth() bool {
	return p.GetAuthenticationStrategy().SupportsSessionAuth()
}

// SupportsMixedAuth checks if policy supports mixed authentication (Ruby: supports_mixed_auth?)
func (p *Policy) SupportsMixedAuth() bool {
	return p.GetAuthenticationStrategy() == AuthMixed
}

// SupportsAuth checks if policy supports any authentication (Ruby: supports_auth?)
func (p *Policy) SupportsAuth() bool {
	return p.GetAuthenticationStrategy().SupportsAuth()
}

// ===== TRANSFER STRATEGY METHODS (Ruby: transfer strategy methods) =====

// GetTransferStrategy returns transfer strategy with default (Ruby: transfer_strategy)
func (p *Policy) GetTransferStrategy() TransferStrategy {
	if p.TransferStrategy == nil {
		return TransferKeepExpiry // Default for backwards compatibility
	}
	return *p.TransferStrategy
}

// ResetsExpiryOnTransfer checks if policy resets expiry on transfer (Ruby: reset_expiry_on_transfer?)
func (p *Policy) ResetsExpiryOnTransfer() bool {
	return p.GetTransferStrategy() == TransferResetExpiry
}

// KeepsExpiryOnTransfer checks if policy keeps expiry on transfer (Ruby: keep_expiry_on_transfer?)
func (p *Policy) KeepsExpiryOnTransfer() bool {
	return p.GetTransferStrategy() == TransferKeepExpiry
}

// ===== LEASING STRATEGY METHODS (Ruby: leasing strategy methods) =====

// GetProcessLeasingStrategy returns process leasing strategy with default (Ruby: process_leasing_strategy)
func (p *Policy) GetProcessLeasingStrategy() LeasingStrategy {
	if p.ProcessLeasing == nil {
		return LeasingPerMachine // Default for backwards compatibility
	}
	return *p.ProcessLeasing
}

// ProcessLeasePerMachine checks if processes lease per machine (Ruby: process_lease_per_machine?)
func (p *Policy) ProcessLeasePerMachine() bool {
	return p.GetProcessLeasingStrategy() == LeasingPerMachine
}

// ProcessLeasePerLicense checks if processes lease per license (Ruby: process_lease_per_license?)
func (p *Policy) ProcessLeasePerLicense() bool {
	return p.GetProcessLeasingStrategy() == LeasingPerLicense
}

// ProcessLeasePerUser checks if processes lease per user (Ruby: process_lease_per_user?)
func (p *Policy) ProcessLeasePerUser() bool {
	return p.GetProcessLeasingStrategy() == LeasingPerUser
}

// GetMachineLeasingStrategy returns machine leasing strategy with default (Ruby: machine_leasing_strategy)
func (p *Policy) GetMachineLeasingStrategy() LeasingStrategy {
	if p.MachineLeasing == nil {
		return LeasingPerLicense // Default for backwards compatibility
	}
	return *p.MachineLeasing
}

// MachineLeasePerLicense checks if machines lease per license (Ruby: machine_lease_per_license?)
func (p *Policy) MachineLeasePerLicense() bool {
	return p.GetMachineLeasingStrategy() == LeasingPerLicense
}

// MachineLeasePerUser checks if machines lease per user (Ruby: machine_lease_per_user?)
func (p *Policy) MachineLeasePerUser() bool {
	return p.GetMachineLeasingStrategy() == LeasingPerUser
}

// ===== OVERAGE STRATEGY METHODS (Ruby: overage strategy methods) =====

// GetOverageStrategy returns overage strategy with default (Ruby: overage_strategy)
func (p *Policy) GetOverageStrategy() OverageStrategy {
	if p.OverageStrategy == nil {
		return OverageNoOverage // Modern default - Go uses latest techniques
	}
	return *p.OverageStrategy
}

// AlwaysAllowsOverage checks if policy always allows overage (Ruby: always_allow_overage?)
func (p *Policy) AlwaysAllowsOverage() bool {
	return p.GetOverageStrategy() == OverageAlwaysAllow
}

// Allows125xOverage checks if policy allows 1.25x overage (Ruby: allow_1_25x_overage?)
func (p *Policy) Allows125xOverage() bool {
	return p.GetOverageStrategy() == OverageAllow125x
}

// Allows15xOverage checks if policy allows 1.5x overage (Ruby: allow_1_5x_overage?)
func (p *Policy) Allows15xOverage() bool {
	return p.GetOverageStrategy() == OverageAllow15x
}

// Allows2xOverage checks if policy allows 2x overage (Ruby: allow_2x_overage?)
func (p *Policy) Allows2xOverage() bool {
	return p.GetOverageStrategy() == OverageAllow2x
}

// AllowsOverage checks if policy allows any overage (Ruby: allow_overage?)
func (p *Policy) AllowsOverage() bool {
	return p.GetOverageStrategy().AllowsOverage()
}

// NoOverage checks if policy allows no overage (Ruby: no_overage?)
func (p *Policy) NoOverage() bool {
	return p.GetOverageStrategy() == OverageNoOverage
}

// ===== DEFAULT VALUE SETTING (Ruby: before_create callbacks) =====

// SetDefaultStrategies sets default strategy values (Ruby: before_create callbacks)
// This replaces Ruby's before_create callbacks with explicit Go method calls
func (p *Policy) SetDefaultStrategies() {
	// Set default machine uniqueness strategy
	if p.MachineUniquenessStrategy == nil {
		strategy := MachineUniquePerLicense
		p.MachineUniquenessStrategy = &strategy
	}

	// Set default machine matching strategy
	if p.MachineMatchingStrategy == nil {
		strategy := MatchAny
		p.MachineMatchingStrategy = &strategy
	}

	// Set default component uniqueness strategy
	if p.ComponentUniquenessStrategy == nil {
		strategy := ComponentUniquePerMachine
		p.ComponentUniquenessStrategy = &strategy
	}

	// Set default component matching strategy
	if p.ComponentMatchingStrategy == nil {
		strategy := MatchAny
		p.ComponentMatchingStrategy = &strategy
	}

	// Set default expiration strategy
	if p.ExpirationStrategy == nil {
		strategy := ExpirationRestrictAccess
		p.ExpirationStrategy = &strategy
	}

	// Set default expiration basis
	if p.ExpirationBasis == nil {
		basis := ExpirationFromCreation
		p.ExpirationBasis = &basis
	}

	// Set default renewal basis
	if p.RenewalBasis == nil {
		basis := RenewalFromExpiry
		p.RenewalBasis = &basis
	}

	// Set default transfer strategy
	if p.TransferStrategy == nil {
		strategy := TransferKeepExpiry
		p.TransferStrategy = &strategy
	}

	// Set default authentication strategy
	if p.AuthenticationStrategy == nil {
		strategy := AuthToken
		p.AuthenticationStrategy = &strategy
	}

	// Set default heartbeat cull strategy
	if p.HeartbeatCullStrategy == nil {
		strategy := HeartbeatDeactivateDead
		p.HeartbeatCullStrategy = &strategy
	}

	// Set default heartbeat resurrection strategy
	if p.HeartbeatResurrectionStrategy == nil {
		strategy := HeartbeatNoRevive
		p.HeartbeatResurrectionStrategy = &strategy
	}

	// Set default machine leasing strategy
	if p.MachineLeasing == nil {
		strategy := LeasingPerLicense
		p.MachineLeasing = &strategy
	}

	// Set default process leasing strategy
	if p.ProcessLeasing == nil {
		strategy := LeasingPerMachine
		p.ProcessLeasing = &strategy
	}

	// Set default overage strategy (Go uses modern default)
	if p.OverageStrategy == nil {
		strategy := OverageNoOverage // Modern default - no overage
		p.OverageStrategy = &strategy
	}

	// Set default heartbeat basis
	if p.HeartbeatBasis == nil {
		if p.RequireHeartbeat {
			basis := HeartbeatFromCreation
			p.HeartbeatBasis = &basis
		} else {
			basis := HeartbeatFromFirstPing
			p.HeartbeatBasis = &basis
		}
	}

	// Set default crypto scheme for modern Go implementation
	if p.Encrypted && p.Scheme == nil {
		scheme := CryptoSchemeED25519 // Modern default - Ed25519
		p.Scheme = &scheme
	}

	// Set max_machines = 1 for node-locked policies
	if p.IsNodeLocked() && p.MaxMachines == nil {
		maxMachines := 1
		p.MaxMachines = &maxMachines
	}
}

// ===== SCOPE REQUIREMENT METHODS (Ruby: require_*_scope? methods) =====

// RequiresProductScope checks if policy requires product scope (Ruby: require_product_scope?)
func (p *Policy) RequiresProductScope() bool {
	return p.RequireProductScope
}

// RequiresPolicyScope checks if policy requires policy scope (Ruby: require_policy_scope?)
func (p *Policy) RequiresPolicyScope() bool {
	return p.RequirePolicyScope
}

// RequiresUserScope checks if policy requires user scope (Ruby: require_user_scope?)
func (p *Policy) RequiresUserScope() bool {
	return p.RequireUserScope
}

// RequiresMachineScope checks if policy requires machine scope (Ruby: require_machine_scope?)
func (p *Policy) RequiresMachineScope() bool {
	return p.RequireMachineScope
}

// RequiresFingerprintScope checks if policy requires fingerprint scope (Ruby: require_fingerprint_scope?)
func (p *Policy) RequiresFingerprintScope() bool {
	return p.RequireFingerprintScope
}

// RequiresComponentsScope checks if policy requires components scope (Ruby: require_components_scope?)
func (p *Policy) RequiresComponentsScope() bool {
	return p.RequireComponentsScope
}

// RequiresChecksumScope checks if policy requires checksum scope (Ruby: require_checksum_scope?)
func (p *Policy) RequiresChecksumScope() bool {
	return p.RequireChecksumScope
}

// RequiresVersionScope checks if policy requires version scope (Ruby: require_version_scope?)
func (p *Policy) RequiresVersionScope() bool {
	return p.RequireVersionScope
}
