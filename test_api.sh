#!/bin/bash

# GoKeys API Test Script
# Tests API endpoints in dependency order: Organization -> Product -> Policy -> License -> Machine

set -e  # Exit on any error

# Configuration
API_BASE="http://localhost:8080/api/v1"
CONTENT_TYPE="Content-Type: application/json"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test function
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local description=$4
    
    log_info "Testing: $description"
    echo "  $method $endpoint"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "$CONTENT_TYPE" \
            -d "$data" \
            "$API_BASE$endpoint")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            "$API_BASE$endpoint")
    fi
    
    # Extract HTTP status code (last line)
    http_code=$(echo "$response" | tail -n1)
    # Extract response body (all but last line)
    body=$(echo "$response" | head -n -1)
    
    echo "  Response Code: $http_code"
    echo "  Response Body: $body"
    
    # Check if request was successful (2xx status codes)
    if [[ $http_code =~ ^2[0-9][0-9]$ ]]; then
        log_success "$description - OK"
        echo "DEBUG: Response body: $body" >&2
        echo "$body"  # Return response body for ID extraction
    else
        log_error "$description - Failed (HTTP $http_code)"
        echo "DEBUG: Error response body: $body" >&2
        echo ""
    fi
    echo "----------------------------------------"
}

# Extract ID from JSON response
extract_id() {
    local json_response=$1
    echo "DEBUG: Trying to extract ID from response: $json_response" >&2

    # Try different ID extraction patterns
    local id=""

    # Pattern 1: "id":"uuid"
    id=$(echo "$json_response" | grep -o '"id":"[^"]*"' | cut -d'"' -f4 | head -n1)
    if [ -n "$id" ]; then
        echo "$id"
        return
    fi

    # Pattern 2: "id": "uuid" (with spaces)
    id=$(echo "$json_response" | grep -o '"id"[[:space:]]*:[[:space:]]*"[^"]*"' | sed 's/.*"\([^"]*\)".*/\1/' | head -n1)
    if [ -n "$id" ]; then
        echo "$id"
        return
    fi

    # Pattern 3: Try with jq if available
    if command -v jq >/dev/null 2>&1; then
        id=$(echo "$json_response" | jq -r '.id // empty' 2>/dev/null)
        if [ -n "$id" ] && [ "$id" != "null" ]; then
            echo "$id"
            return
        fi
    fi

    echo "DEBUG: Could not extract ID from response" >&2
    echo ""
}

echo "=========================================="
echo "GoKeys API Test Suite"
echo "=========================================="

# Check if server is running
log_info "Checking if server is running..."
if ! curl -s "$API_BASE/../health" > /dev/null; then
    log_error "Server is not running at $API_BASE"
    log_info "Please start the server with: ./main"
    exit 1
fi
log_success "Server is running"
echo "----------------------------------------"

# 1. Test Organization endpoints
echo "1. TESTING ORGANIZATIONS"
echo "=========================================="

# Create Organization
org_response=$(test_endpoint "POST" "/organizations" '{
    "name": "Test Organization",
    "description": "A test organization for API testing",
    "website": "https://test-org.com",
    "email": "<EMAIL>"
}' "Create Organization")

ORGANIZATION_ID=$(extract_id "$org_response")
if [ -z "$ORGANIZATION_ID" ]; then
    log_error "Failed to extract organization ID"
    exit 1
fi
log_info "Organization ID: $ORGANIZATION_ID"

# List Organizations
test_endpoint "GET" "/organizations" "" "List Organizations"

# Get Organization by ID
test_endpoint "GET" "/organizations/$ORGANIZATION_ID" "" "Get Organization by ID"

# Update Organization
test_endpoint "PUT" "/organizations/$ORGANIZATION_ID" '{
    "name": "Updated Test Organization",
    "description": "Updated description for testing"
}' "Update Organization"

echo ""

# 2. Test Product endpoints
echo "2. TESTING PRODUCTS"
echo "=========================================="

# Create Product
product_response=$(test_endpoint "POST" "/products" '{
    "organizationId": "'$ORGANIZATION_ID'",
    "name": "Test Product",
    "description": "A test product for API testing",
    "version": "1.0.0"
}' "Create Product")

PRODUCT_ID=$(extract_id "$product_response")
if [ -z "$PRODUCT_ID" ]; then
    log_error "Failed to extract product ID"
    exit 1
fi
log_info "Product ID: $PRODUCT_ID"

# List Products
test_endpoint "GET" "/products" "" "List Products"

# Get Product by ID
test_endpoint "GET" "/products/$PRODUCT_ID" "" "Get Product by ID"

# Update Product
test_endpoint "PUT" "/products/$PRODUCT_ID" '{
    "name": "Updated Test Product",
    "version": "1.1.0"
}' "Update Product"

echo ""

# 3. Test Policy endpoints
echo "3. TESTING POLICIES"
echo "=========================================="

# Create Policy
policy_response=$(test_endpoint "POST" "/policies" '{
    "name": "Test Policy",
    "productId": "'$PRODUCT_ID'",
    "description": "A test policy for API testing",
    "strict": true,
    "floating": false,
    "requireHeartbeat": true,
    "heartbeatDuration": 3600,
    "maxMachines": 5,
    "maxCores": 16,
    "maxUses": 100,
    "maxUsers": 10,
    "maxProcesses": 20
}' "Create Policy")

POLICY_ID=$(extract_id "$policy_response")
if [ -z "$POLICY_ID" ]; then
    log_error "Failed to extract policy ID"
    exit 1
fi
log_info "Policy ID: $POLICY_ID"

# List Policies
test_endpoint "GET" "/policies" "" "List Policies"

# Get Policy by ID
test_endpoint "GET" "/policies/$POLICY_ID" "" "Get Policy by ID"

# Update Policy
test_endpoint "PUT" "/policies/$POLICY_ID" '{
    "name": "Updated Test Policy",
    "maxMachines": 10
}' "Update Policy"

echo ""

# 4. Test License endpoints
echo "4. TESTING LICENSES"
echo "=========================================="

# Create License
license_response=$(test_endpoint "POST" "/licenses" '{
    "policyId": "'$POLICY_ID'",
    "organizationId": "'$ORGANIZATION_ID'",
    "name": "Test License",
    "ownerType": "organization",
    "ownerId": "'$ORGANIZATION_ID'",
    "protected": false
}' "Create License")

LICENSE_ID=$(extract_id "$license_response")
if [ -z "$LICENSE_ID" ]; then
    log_error "Failed to extract license ID"
    exit 1
fi
log_info "License ID: $LICENSE_ID"

# List Licenses
test_endpoint "GET" "/licenses" "" "List Licenses"

# Get License by ID
test_endpoint "GET" "/licenses/$LICENSE_ID" "" "Get License by ID"

# Update License
test_endpoint "PUT" "/licenses/$LICENSE_ID" '{
    "name": "Updated Test License"
}' "Update License"

# Test License Actions
test_endpoint "POST" "/licenses/$LICENSE_ID/actions/suspend" '{}' "Suspend License"
test_endpoint "POST" "/licenses/$LICENSE_ID/actions/reinstate" '{}' "Reinstate License"

# Test License Validation (Public endpoint)
test_endpoint "POST" "/licenses/$LICENSE_ID/actions/validate" '{}' "Validate License"

echo ""

# 5. Test Machine endpoints
echo "5. TESTING MACHINES"
echo "=========================================="

# Create Machine
machine_response=$(test_endpoint "POST" "/machines" '{
    "licenseId": "'$LICENSE_ID'",
    "name": "Test Machine",
    "fingerprint": "test-machine-fingerprint-123",
    "platform": "linux",
    "hostname": "test-host",
    "cores": 4,
    "ip": "*************"
}' "Create Machine")

MACHINE_ID=$(extract_id "$machine_response")
if [ -z "$MACHINE_ID" ]; then
    log_error "Failed to extract machine ID"
    exit 1
fi
log_info "Machine ID: $MACHINE_ID"

# List Machines
test_endpoint "GET" "/machines" "" "List Machines"

# Get Machine by ID
test_endpoint "GET" "/machines/$MACHINE_ID" "" "Get Machine by ID"

# Update Machine
test_endpoint "PUT" "/machines/$MACHINE_ID" '{
    "name": "Updated Test Machine",
    "cores": 8
}' "Update Machine"

# Test Machine Heartbeat (Public endpoint)
test_endpoint "POST" "/machines/$MACHINE_ID/actions/heartbeat" '{}' "Machine Heartbeat"

echo ""

# 6. Test User endpoints
echo "6. TESTING USERS"
echo "=========================================="

# Create User
user_response=$(test_endpoint "POST" "/users" '{
    "email": "<EMAIL>",
    "password": "testpassword123",
    "firstName": "Test",
    "lastName": "User"
}' "Create User")

USER_ID=$(extract_id "$user_response")
if [ -z "$USER_ID" ]; then
    log_error "Failed to extract user ID"
    exit 1
fi
log_info "User ID: $USER_ID"

# List Users
test_endpoint "GET" "/users" "" "List Users"

# Get User by ID
test_endpoint "GET" "/users/$USER_ID" "" "Get User by ID"

# Update User
test_endpoint "PUT" "/users/$USER_ID" '{
    "firstName": "Updated Test",
    "lastName": "Updated User"
}' "Update User"

echo ""

# 7. Cleanup (Delete in reverse order)
echo "7. CLEANUP - DELETING RESOURCES"
echo "=========================================="

log_info "Cleaning up created resources..."

# Delete Machine
test_endpoint "DELETE" "/machines/$MACHINE_ID" "" "Delete Machine"

# Delete License
test_endpoint "DELETE" "/licenses/$LICENSE_ID" "" "Delete License"

# Delete Policy
test_endpoint "DELETE" "/policies/$POLICY_ID" "" "Delete Policy"

# Delete Product
test_endpoint "DELETE" "/products/$PRODUCT_ID" "" "Delete Product"

# Delete Organization
test_endpoint "DELETE" "/organizations/$ORGANIZATION_ID" "" "Delete Organization"

# Delete User
test_endpoint "DELETE" "/users/$USER_ID" "" "Delete User"

echo ""
echo "=========================================="
log_success "API Test Suite Completed!"
echo "=========================================="
