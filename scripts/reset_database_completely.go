package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/lib/pq"
)

func main() {
	// Connect to database
	dbURL := "postgres://gokeys:gokeys_dev_password@localhost:5432/gokeys_dev?sslmode=disable"
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Test connection
	if err := db.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}

	fmt.Println("Connected to database successfully")

	// Drop all tables
	fmt.Println("Dropping all tables...")
	
	tables := []string{
		"schema_migrations",
		"machines",
		"licenses", 
		"policies",
		"products",
		"api_tokens",
		"sessions",
		"permissions",
		"users_organizations",
		"users",
		"organizations",
	}
	
	for _, table := range tables {
		_, err = db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s CASCADE", table))
		if err != nil {
			fmt.Printf("Warning: Failed to drop table %s: %v\n", table, err)
		} else {
			fmt.Printf("Dropped table: %s\n", table)
		}
	}

	fmt.Println("All tables dropped. You can now run the server.")
}
