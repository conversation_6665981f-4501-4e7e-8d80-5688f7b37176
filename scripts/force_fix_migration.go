package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/lib/pq"
)

func main() {
	// Connect to database
	dbURL := "postgres://gokeys:gokeys_dev_password@localhost:5432/gokeys_dev?sslmode=disable"
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Test connection
	if err := db.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}

	fmt.Println("Connected to database successfully")

	// Drop schema_migrations table completely
	fmt.Println("Dropping schema_migrations table...")
	_, err = db.Exec("DROP TABLE IF EXISTS schema_migrations")
	if err != nil {
		log.Fatal("Failed to drop schema_migrations table:", err)
	}

	fmt.Println("Schema migrations table dropped. You can now run the server.")
}
