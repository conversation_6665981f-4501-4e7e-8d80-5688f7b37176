package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/lib/pq"
)

func main() {
	// First connect to postgres database to create gokeys_dev if needed
	adminURL := "postgres://gokeys:gokeys_dev_password@localhost:5432/postgres?sslmode=disable"
	adminDB, err := sql.Open("postgres", adminURL)
	if err != nil {
		log.Fatal("Failed to connect to admin database:", err)
	}
	defer adminDB.Close()

	// Drop and recreate database
	fmt.Println("Dropping existing database...")
	_, err = adminDB.Exec("DROP DATABASE IF EXISTS gokeys_dev")
	if err != nil {
		log.Fatal("Failed to drop database:", err)
	}

	fmt.Println("Creating fresh database...")
	_, err = adminDB.Exec("CREATE DATABASE gokeys_dev")
	if err != nil {
		log.Fatal("Failed to create database:", err)
	}
	adminDB.Close()

	// Now connect to the target database
	dbURL := "postgres://gokeys:gokeys_dev_password@localhost:5432/gokeys_dev?sslmode=disable"

	// Connect to database
	db, err := sql.Open("postgres", dbURL)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Test connection
	if err := db.Ping(); err != nil {
		log.Fatal("Failed to ping database:", err)
	}

	fmt.Println("Connected to database successfully")

	// Fix dirty migration state
	fmt.Println("Fixing dirty migration state...")
	_, err = db.Exec("UPDATE schema_migrations SET dirty = false WHERE version = 4")
	if err != nil {
		fmt.Printf("Warning: Failed to fix dirty state: %v\n", err)
	}

	// Set migration version back to 3 to skip the problematic migration
	fmt.Println("Setting migration version to 3...")
	_, err = db.Exec("UPDATE schema_migrations SET version = 3 WHERE version = 4")
	if err != nil {
		fmt.Printf("Warning: Failed to set version: %v\n", err)
	}

	fmt.Println("Migration state fixed! You can now run the server.")
}
