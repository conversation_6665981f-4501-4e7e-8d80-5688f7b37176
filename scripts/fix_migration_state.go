package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/lib/pq"
)

func main() {
	// First connect to postgres database to drop and recreate gokeys_dev
	adminURL := "postgres://gokeys:gokeys_dev_password@localhost:5432/postgres?sslmode=disable"
	adminDB, err := sql.Open("postgres", adminURL)
	if err != nil {
		log.Fatal("Failed to connect to admin database:", err)
	}
	defer adminDB.Close()

	// Drop and recreate database completely
	fmt.Println("Dropping existing database completely...")
	_, err = adminDB.Exec("DROP DATABASE IF EXISTS gokeys_dev")
	if err != nil {
		log.Fatal("Failed to drop database:", err)
	}

	fmt.Println("Creating fresh database...")
	_, err = adminDB.Exec("CREATE DATABASE gokeys_dev")
	if err != nil {
		log.Fatal("Failed to create database:", err)
	}

	fmt.Println("Database completely reset! You can now run the server.")
}
